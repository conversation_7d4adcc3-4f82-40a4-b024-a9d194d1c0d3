"""
<PERSON>ript to compile the LaTeX document and generate PDF
"""

import subprocess
import os
import sys

def compile_latex():
    """
    Compile the LaTeX document to PDF
    """
    print("Compiling LaTeX document...")
    
    # Check if pdflatex is available
    try:
        subprocess.run(['pdflatex', '--version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Error: pdflatex not found. Please install a LaTeX distribution.")
        print("For Windows: Install MiKTeX or TeX Live")
        print("For Linux: sudo apt-get install texlive-full")
        print("For macOS: Install MacTeX")
        return False
    
    # Compile the document (run twice for cross-references)
    tex_file = "droplet_combustion_physics.tex"
    
    if not os.path.exists(tex_file):
        print(f"Error: {tex_file} not found!")
        return False
    
    try:
        print("First compilation pass...")
        result1 = subprocess.run(['pdflatex', '-interaction=nonstopmode', tex_file], 
                                capture_output=True, text=True)
        
        print("Second compilation pass...")
        result2 = subprocess.run(['pdflatex', '-interaction=nonstopmode', tex_file], 
                                capture_output=True, text=True)
        
        if result2.returncode == 0:
            print("✓ PDF compilation successful!")
            print("Generated: droplet_combustion_physics.pdf")
            
            # Clean up auxiliary files
            aux_extensions = ['.aux', '.log', '.out', '.toc']
            for ext in aux_extensions:
                aux_file = tex_file.replace('.tex', ext)
                if os.path.exists(aux_file):
                    os.remove(aux_file)
            
            return True
        else:
            print("✗ PDF compilation failed!")
            print("LaTeX output:")
            print(result2.stdout)
            print(result2.stderr)
            return False
            
    except Exception as e:
        print(f"Error during compilation: {e}")
        return False

def create_readme():
    """
    Create a README file explaining the document
    """
    readme_content = """# Droplet Combustion Physics Documentation

This directory contains a comprehensive LaTeX document describing the physical model and computational framework for single-component m-xylene droplet combustion.

## Files

- `droplet_combustion_physics.tex` - Main LaTeX source file
- `droplet_combustion_physics.pdf` - Compiled PDF document
- `compile_latex.py` - Python script to compile the LaTeX document

## Document Contents

1. **Introduction** - Overview of droplet combustion physics
2. **Physical Model** - Mathematical formulation of governing equations
3. **Thermophysical Properties** - Gas mixture property calculations
4. **Boundary Conditions** - Coupling between droplet and gas phases
5. **Numerical Implementation** - Finite difference methods and algorithms
6. **m-Xylene Properties** - Specific fuel characteristics and chemistry
7. **Implementation Details** - Code structure and computational procedures
8. **Validation** - Model verification and experimental comparison

## Key Features

- Detailed mathematical derivations
- Complete thermophysical property models
- Numerical implementation details
- Physical property correlations for m-xylene
- Comprehensive nomenclature

## Compilation

To compile the PDF:

```bash
python compile_latex.py
```

Or manually with LaTeX:

```bash
pdflatex droplet_combustion_physics.tex
pdflatex droplet_combustion_physics.tex  # Run twice for cross-references
```

## Requirements

- LaTeX distribution (MiKTeX, TeX Live, or MacTeX)
- Required packages: amsmath, amssymb, graphicx, float, geometry, hyperref, cite, array, booktabs

## Usage

This document serves as:
- Technical reference for the droplet combustion model
- Documentation for the computational implementation
- Guide for understanding the physical processes
- Basis for further model development and validation
"""
    
    with open("README.md", "w") as f:
        f.write(readme_content)
    
    print("Created README.md")

if __name__ == "__main__":
    print("LaTeX Document Compilation Script")
    print("=" * 40)
    
    # Create README
    create_readme()
    
    # Compile LaTeX
    success = compile_latex()
    
    if success:
        print("\n✓ All tasks completed successfully!")
        print("You can now view the PDF document: droplet_combustion_physics.pdf")
    else:
        print("\n✗ Compilation failed. Please check the LaTeX installation and file.")
        sys.exit(1)
