%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Scribe notes style file      
%
% This file should be called scribe.sty
%
% Your main LaTeX file should look like this:
%
%   \documentclass[12pt]{article}
%   \usepackage{scribe}
%
%   \Scribe{YOUR NAME}
%   \Lecturer{<PERSON><PERSON><PERSON> Gupta OR <PERSON>'Donnell}
%   \LectureNumber{N}
%   \LectureDate{DATE}
%   \LectureTitle{A TITLE FOR THE LECTURE}
%
%   \begin{document}
%   \MakeScribeTop
%
%   \section{SECTION NAME}
%
%   NOTES GO HERE
%
%   \section{ANOTHER SECTION NAME}
%
%   MORE NOTES GO HERE
%
%   etc.
%
%   \bibliographystyle{abbrv}           % if you need a bibliography
%   \bibliography{mybib}                % assuming yours is named mybib.bib
%
%   \end{document}
%
%
% A .bib file is a text file containing a sequence like...
% 
%   @article{ADR82,
%     author = "<PERSON> and <PERSON> and <PERSON>{\'e}rard <PERSON>",
%     title = "Experimental Test of {B}ell's Inequalities Using Time-Varying Analyzers",
%     journal = "Phys.\ Rev.\ Lett.",
%     volume = 49,
%     number = 25,
%     pages = "1804--1807",
%     year = 1982
%   }
%
%   @inproceedings{Fei91,
%     author = "Uriel Feige",
%     title = "On the success probability of the two provers in one round proof systems",
%     booktitle = "Proc.\ 6th Symp.\ on Structure in Complexity Theory (CCC)",
%     pages = "116--123",
%     year = 1991
%   }
%
%
%
%
%
%
% For your LaTeX files, there are some macros you may want to use below...

\usepackage[margin=1in]{geometry}

% \oddsidemargin 0in \evensidemargin 0in \marginparwidth 10pt
% \marginparsep 10pt \topmargin 0pt \headsep 0in \headheight 0in
% \textheight 8.5in \textwidth 6.5in \brokenpenalty=10000

\usepackage{amssymb}
\usepackage{amsfonts}
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{latexsym}
\usepackage{epsfig}
\usepackage{bm}
\usepackage{xspace}
\usepackage{times}
\usepackage[utf8x]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{listings}
\usepackage{color}
 
\definecolor{codegreen}{rgb}{0.3,0.6,0.4}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}
 
\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},   
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\footnotesize,
    breakatwhitespace=false,         
    breaklines=true,                 
    captionpos=b,                    
    keepspaces=true,                 
    numbers=left,                    
    numbersep=5pt,                  
    showspaces=false,                
    showstringspaces=false,
    showtabs=false,                  
    tabsize=2
}

%%
%% Julia definition (c) 2014 Jubobs
%%
\lstdefinelanguage{Julia}%
  {morekeywords={abstract,break,case,catch,const,continue,do,else,elseif,%
      end,export,false,for,function,immutable,import,importall,if,in,%
      macro,module,otherwise,quote,return,switch,true,try,type,typealias,%
      using,while},%
   sensitive=true,%
   alsoother={$},%
   morecomment=[l]\#,%
   morecomment=[n]{\#=}{=\#},%
   morestring=[s]{"}{"},%
   morestring=[m]{'}{'},%
}[keywords,comments,strings]%

\lstset{%
    language         = Julia,
    basicstyle       = \ttfamily,
    keywordstyle     = \bfseries\color{blue},
    stringstyle      = \color{magenta},
    commentstyle     = \color{ForestGreen},
    showstringspaces = false,
}


\newtheorem{theorem}{Theorem}[section]
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{fact}[theorem]{Fact}
\newtheorem{example}[theorem]{Example}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{observation}[theorem]{Observation}
\newtheorem{conjecture}[theorem]{Conjecture}

\theoremstyle{definition}
\newtheorem{definition}[theorem]{Definition}

\theoremstyle{remark}
\newtheorem{remark}[theorem]{Remark}

% Setting the theorem style back to plain in case theorems are defined in the main file
\theoremstyle{plain}

\usepackage{indentfirst} 
\setlength{\parindent}{2em}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Useful macros
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% for temporarily chunks of text
\newcommand{\ignore}[1]{}

% Probability/expectation operators.  The ones ending in x should be used if you want
% subscripts that go directly *below* the operator (in math mode); no x means the subscripts
% go below and to the right.  NB:  \P is remapped below for the complexity class P.
\renewcommand{\Pr}{{\bf Pr}}
\newcommand{\Prx}{\mathop{\bf Pr\/}}
\newcommand{\E}{{\bf E}}
\newcommand{\Ex}{\mathop{\bf E\/}}
\newcommand{\Var}{{\bf Var}}
\newcommand{\Varx}{\mathop{\bf Var\/}}
\newcommand{\Cov}{{\bf Cov}}
\newcommand{\Covx}{\mathop{\bf Cov\/}}

% shortcuts for symbol names that are too long to type
\newcommand{\eps}{\epsilon}
\newcommand{\lam}{\lambda}
\renewcommand{\l}{\ell}
\newcommand{\la}{\langle}
\newcommand{\ra}{\rangle}
\newcommand{\wh}{\widehat}
\newcommand{\wt}{\widetilde}

% "blackboard-fonted" letters for the reals, naturals etc.
\newcommand{\R}{\mathbb R}
\newcommand{\N}{\mathbb N}
\newcommand{\Z}{\mathbb Z}
\newcommand{\F}{\mathbb F}
\newcommand{\Q}{\mathbb Q}
\newcommand{\C}{\mathbb C}

% operators that should be typeset in Roman font
\newcommand{\poly}{\mathrm{poly}}
\newcommand{\polylog}{\mathrm{polylog}}
\newcommand{\sgn}{\mathrm{sgn}}
\newcommand{\avg}{\mathop{\mathrm{avg}}}
\newcommand{\val}{{\mathrm{val}}}

% complexity classes
\renewcommand{\P}{\mathrm{P}}
\newcommand{\NP}{\mathrm{NP}}
\newcommand{\BPP}{\mathrm{BPP}}
\newcommand{\DTIME}{\mathrm{DTIME}}
\newcommand{\ZPTIME}{\mathrm{ZPTIME}}
\newcommand{\BPTIME}{\mathrm{BPTIME}}
\newcommand{\NTIME}{\mathrm{NTIME}}

% values associated to optimization algorithm instances
\newcommand{\Opt}{{\mathsf{Opt}}}
\newcommand{\Alg}{{\mathsf{Alg}}}
\newcommand{\Lp}{{\mathsf{Lp}}}
\newcommand{\Sdp}{{\mathsf{Sdp}}}
\newcommand{\Exp}{{\mathsf{Exp}}}

% if you think the sum and product signs are too big in your math mode; x convention
% as in the probability operators
\newcommand{\littlesum}{{\textstyle \sum}}
\newcommand{\littlesumx}{\mathop{{\textstyle \sum}}}
\newcommand{\littleprod}{{\textstyle \prod}}
\newcommand{\littleprodx}{\mathop{{\textstyle \prod}}}

% horizontal line across the page
\newcommand{\horz}{
\vspace{-.4in}
\begin{center}
\begin{tabular}{p{\textwidth}}\\
\hline
\end{tabular}
\end{center}
}

% calligraphic letters
\newcommand{\calA}{{\cal A}}
\newcommand{\calB}{{\cal B}}
\newcommand{\calC}{{\cal C}}
\newcommand{\calD}{{\cal D}}
\newcommand{\calE}{{\cal E}}
\newcommand{\calF}{{\cal F}}
\newcommand{\calG}{{\cal G}}
\newcommand{\calH}{{\cal H}}
\newcommand{\calI}{{\cal I}}
\newcommand{\calJ}{{\cal J}}
\newcommand{\calK}{{\cal K}}
\newcommand{\calL}{{\cal L}}
\newcommand{\calM}{{\cal M}}
\newcommand{\calN}{{\cal N}}
\newcommand{\calO}{{\cal O}}
\newcommand{\calP}{{\cal P}}
\newcommand{\calQ}{{\cal Q}}
\newcommand{\calR}{{\cal R}}
\newcommand{\calS}{{\cal S}}
\newcommand{\calT}{{\cal T}}
\newcommand{\calU}{{\cal U}}
\newcommand{\calV}{{\cal V}}
\newcommand{\calW}{{\cal W}}
\newcommand{\calX}{{\cal X}}
\newcommand{\calY}{{\cal Y}}
\newcommand{\calZ}{{\cal Z}}

% bold letters (useful for random variables)
\renewcommand{\a}{{\boldsymbol a}}
\renewcommand{\b}{{\boldsymbol b}}
\renewcommand{\c}{{\boldsymbol c}}
\renewcommand{\d}{{\boldsymbol d}}
\newcommand{\e}{{\boldsymbol e}}
\newcommand{\f}{{\boldsymbol f}}
\newcommand{\g}{{\boldsymbol g}}
\newcommand{\h}{{\boldsymbol h}}
\renewcommand{\i}{{\boldsymbol i}}
\renewcommand{\j}{{\boldsymbol j}}
\renewcommand{\k}{{\boldsymbol k}}
\newcommand{\m}{{\boldsymbol m}}
\newcommand{\n}{{\boldsymbol n}}
\renewcommand{\o}{{\boldsymbol o}}
\newcommand{\p}{{\boldsymbol p}}
\newcommand{\q}{{\boldsymbol q}}
\renewcommand{\r}{{\boldsymbol r}}
\newcommand{\s}{{\boldsymbol s}}
\renewcommand{\t}{{\boldsymbol t}}
\renewcommand{\u}{{\boldsymbol u}}
\renewcommand{\v}{{\boldsymbol v}}
\newcommand{\w}{{\boldsymbol w}}
\newcommand{\x}{{\boldsymbol x}}
\newcommand{\y}{{\boldsymbol y}}
\newcommand{\z}{{\boldsymbol z}}
\newcommand{\A}{{\boldsymbol A}}
\newcommand{\B}{{\boldsymbol B}}
\newcommand{\D}{{\boldsymbol D}}
\newcommand{\G}{{\boldsymbol G}}
\renewcommand{\H}{{\boldsymbol H}}
\newcommand{\I}{{\boldsymbol I}}
\newcommand{\J}{{\boldsymbol J}}
\newcommand{\K}{{\boldsymbol K}}
\renewcommand{\L}{{\boldsymbol L}}
\newcommand{\M}{{\boldsymbol M}}
\renewcommand{\O}{{\boldsymbol O}}
\renewcommand{\S}{{\boldsymbol S}}
\newcommand{\T}{{\boldsymbol T}}
\newcommand{\U}{{\boldsymbol U}}
\newcommand{\V}{{\boldsymbol V}}
\newcommand{\W}{{\boldsymbol W}}
\newcommand{\X}{{\boldsymbol X}}
\newcommand{\Y}{{\boldsymbol Y}}



% useful for Fourier analysis
\newcommand{\bits}{\{-1,1\}}
\newcommand{\bitsn}{\{-1,1\}^n}
\newcommand{\bn}{\bitsn}
\newcommand{\isafunc}{{: \bitsn \rightarrow \bits}}
\newcommand{\fisafunc}{{f : \bitsn \rightarrow \bits}}

% if you want
\newcommand{\half}{{\textstyle \frac12}}

\newcommand{\myfig}[4]{\begin{figure}[h] \begin{center} \includegraphics[width=#1\textwidth]{#2} \caption{#3} \label{#4} \end{center} \end{figure}} 


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Feel free to ignore the rest of this file



\def\ScribeStr{??}
\def\LecStr{??}
\def\LecNum{??}
\def\LecTitle{??}
\def\LecDate{??}
\newcommand{\Scribe}[1]{\def\ScribeStr{Scribe: #1}}
\newcommand{\Scribes}[1]{\def\ScribeStr{Scribes: #1}}
\newcommand{\Lecturer}[1]{\def\LecStr{Lecturer: #1}}
\newcommand{\Lecturers}[1]{\def\LecStr{Lecturers: #1}}
\newcommand{\LectureNumber}[1]{\def\LecNum{#1}}
\newcommand{\LectureDate}[1]{\def\LecDate{#1}}
\newcommand{\LectureTitle}[1]{\def\LecTitle{#1}}

\newdimen\headerwidth

\newcommand{\MakeScribeTop}{
\noindent
\begin{center}
  \framebox{
    \vbox{
      \headerwidth=\textwidth
      \advance\headerwidth by -0.22in
      \hbox to \headerwidth {\hfill BUAA SaC Lab}
      \vspace{4mm}
      \hbox to \headerwidth {{\Large \hfill {\LecTitle} \hfill}}
      \vspace{2mm}
      \hbox to \headerwidth {\hfill \LecDate \hfill}
      \vspace{2mm}
      \hbox to \headerwidth {{\it \LecStr \hfill \ScribeStr}}
      }
    }
\end{center}
\vspace*{4mm}}
