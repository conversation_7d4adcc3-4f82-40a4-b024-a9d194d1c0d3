import numpy as np
import cantera as ct
import matplotlib.pyplot as plt

# 加载铝表面机理和气相机理
surf = ct.Interface('./mechs/Al_surface.yaml', name='Al_surface')
gas = surf.adjacent["gas"]  # 气相
bulk1 = surf.adjacent["bulk1"]  # 第一个固体相
bulk2 = surf.adjacent["bulk2"]  # 第二个固体相

# 设置温度
t = 2500.0  # 温度 [K]
gas.TPX = t, ct.one_atm, 'O2:0.21, AR:0.79'  # 设置气相的温度、压力和组分
surf.TPX = t, ct.one_atm, "AL(L):1.0"  # 设置表面的温度和压力
bulk1.TPX = t, ct.one_atm, 'AL(B):1.0'  # 设置bulk1的温度和压力
bulk2.TPX = t, ct.one_atm, 'AL2O3(B):1.0'  # 设置bulk2的温度和压力

nsp = gas.n_species  # 获取气相物质种类数量
nSurfSp = surf.n_species  # 获取表面物质种类数量

# 创建定容定温反应器
r = ct.IdealGasReactor(gas)
r.volume = 1.0 # e-6
rsurf = ct.ReactorSurface(surf, r)
rsurf.area = 0.4 # e-4
r.energy_enabled = False
network = ct.ReactorNet([r])  # 创建反应器网络

# 解决反应器的时间演化
t_end = 0.01  # 设定结束时间 [秒]
n_steps = 1000  # 设置时间步数
time = np.logspace(-8, -2, n_steps)  # 创建对数刻度的时间序列

# 存储温度和压力数据
temps = []
pressures = []

# 存储气相和表面物种的浓度数据
AL_gas_conc = []
O2_gas_conc = []
O_surf_conc = []
surf_rec_0 = []

# 循环解决反应器的状态
for i in range(n_steps):
	network.advance(time[i])  # 在时间步长内推进反应器网络
	temps.append(r.T)  # 记录当前温度
	pressures.append(r.thermo.P)  # 记录当前压力

	# 获取气相中的物质浓度
	AL_gas_conc.append(gas['AL'].X[0])  # 气相中铝的浓度
	O2_gas_conc.append(gas['O2'].X[0])  # 气相中氧气的浓度
	surf_rec_0.append(surf.net_rates_of_progress[0])

	# 获取表面中的物质浓度（覆盖度）
	O_surf_conc.append(surf.coverages[surf.species_index('O(S)')])  # 表面中氧的覆盖度
	# O_surf_conc.append(surf.net_rates_of_progress[0])  # 表面中氧的覆盖度

# 可视化结果
fig, ax = plt.subplots(2, 2, figsize=(10, 8))

# 绘制温度和压力随时间变化
ax[0, 0].plot(time, temps, label='Temperature (K)')
ax[0, 0].set_xlabel('Time [s]')
ax[0, 0].set_ylabel('Temperature [K]')
ax[0, 0].set_title('Temperature vs Time')
ax[0, 0].set_xscale('log')  # 设置对数刻度

ax[0, 1].plot(time, pressures, label='Pressure (Pa)', color='r')
ax[0, 1].set_xlabel('Time [s]')
ax[0, 1].set_ylabel('Pressure [Pa]')
ax[0, 1].set_title('Pressure vs Time')
ax[0, 1].set_xscale('log')  # 设置对数刻度

# 气相物质浓度
ax[1, 0].plot(time, AL_gas_conc, label='AL (gas)', color='b')
ax[1, 0].plot(time, O2_gas_conc, label='O2 (gas)', color='g')
# ax[1, 0].plot(time, O_surf_conc, label='O(S) (surface)', color='orange')
ax[1, 0].set_xlabel('Time [s]')
ax[1, 0].set_ylabel('Mole Fraction')
ax[1, 0].set_title('Gas Phase Species Mole Fractions')
ax[1, 0].legend()
ax[1, 0].set_xscale('log')  # 设置对数刻度

# 表面物质浓度
# ax[1, 1].plot(time, surf_rec_0, label='AL + AL(L) <=> AL(B) + AL(L)', color='g')
ax[1, 1].plot(time, O_surf_conc, label='O(S) (surface)', color='orange')
ax[1, 1].set_xlabel('Time [s]')
ax[1, 1].set_ylabel('Surface Coverage')
ax[1, 1].set_title('Surface Species Coverage vs Time')
ax[1, 1].legend()
ax[1, 1].set_xscale('log')  # 设置对数刻度

plt.tight_layout()
plt.savefig("results_of_mine.png")
plt.show()