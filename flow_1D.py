# Copyright 2024 <PERSON>, <PERSON><PERSON>, Beihang University. 
# E-mail: z<PERSON><EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License. 

"""1D steady flow model for aluminum droplet combustion."""
from typing import Tuple
import numpy as np
from einops import rearrange
import cantera as ct
from scipy.optimize import Bounds, LinearConstraint, minimize
from scipy.integrate import solve_ivp
from scipy.optimize import root

import matplotlib.pyplot as plt
from matplotlib import rcParams
config = {
	"font.family":'sans serif',
	"font.size": 12,
	"mathtext.fontset":'custom',
	"font.sans-serif": ['Arial'],
	"axes.unicode_minus":False,
}
rcParams.update(config)
labelsize = 12


# computational domain
dr = 2e-6 # um
r_min = 105e-6 # um
r_max = r_min * 15
n_r = int((r_max - r_min) / dr)
r = np.linspace(r_min, r_max, n_r)
dr = r[1] - r[0]
R = np.meshgrid(r)[0]         # face of cells, shape (n_r,)
cell_R = (R[1:] + R[:-1]) / 2 # center of cells, shape (n_r-1,)


# initial scalar field
far_T = 2500 # far field temperature
surf_T = 2650 # surface temperature
gas = ct.Solution("./mechs/Al_gas.yaml")
gas.TPX = far_T, ct.one_atm, {'O2':0.21, 'AR':0.79}
scalar_gas = np.zeros((gas.n_species + 1, cell_R.shape[0])) # T,Y at cells
# f = lambda x: 2717.745 + 12.35727 * x - 0.04548113 * x*x + 0.00005990919 * x*x*x - 2.713432e-8 * x*x*x*x
# scalar0[0] = f(np.arange(cell_R.shape[0])/1.26+1)
scalar_gas[0] = far_T # initial gas temperature
# scalar_gas[1] = 0.8**((np.arange(cell_R.shape[0])+1) ** (1/1.01))
scalar_gas[1] = 0.5**((np.arange(cell_R.shape[0])+1) ** (1/3.0)) # 1.8))
scalar_gas[-5] = (np.arange(cell_R.shape[0])/100 + 0.2) * np.exp(-np.arange(cell_R.shape[0])/100) * 1.5
# scalar0[-5] = 0.3**((np.arange(cell_R.shape[0])+1) ** (1/3.5)) # 1.8))
# scalar0[1] = 0.99**((np.arange(cell_R.shape[0]) * 100) ** (1/1.5))
# scalar0[1] = 0.0 # 0.8**((np.arange(cell_R.shape[0])+1) ** (1/2.5))
scalar_gas[-3] = (1 - scalar_gas[1] - scalar_gas[-5]) * gas.Y[-3] # initial Y of oxygen
scalar_gas[-2] = (1 - scalar_gas[1] - scalar_gas[-5]) * gas.Y[-2] # initial Y of argon
scalar_gas[1:] /= scalar_gas[1:].sum(axis=0) 
print(R.shape, scalar_gas.shape, scalar_gas[-3][0])


# def scalar2gas(scalar: np.ndarray) -> Tuple[np.ndarray]:
#   "Encode scalar arrays to gas quantities."

#   # thermochemical properties in cells
#   gas = ct.Solution("./mechs/Al_gas.yaml")
#   gas_array = ct.SolutionArray(gas, shape=cell_R.shape[0])
#   gas_array.TPY = scalar[0], ct.one_atm, scalar[1:].T
#   Xk = gas_array.X.T # kmol/kmol
#   Dk = gas_array.mix_diff_coeffs.T # m2/s
#   rho = gas_array.density # kg/m3
#   cp = gas_array.cp # J/kg/K
#   lam = gas_array.thermal_conductivity # W/m/K = kg*m2/s2/s/m/K
#   hk = (gas_array.partial_molar_enthalpies / gas_array.molecular_weights).T # J/kg = m2/s2
#   qdot = gas_array.heat_release_rate # W/m3 = J/s/m3 = kg/m/s3
#   mdotk = (gas_array.net_production_rates * gas_array.molecular_weights).T # kg/m3/s
#   grad_Xk = np.zeros_like(Xk) # kmol/kmol/m
#   grad_Xk[:, :-1] = (Xk[:, 1:] - Xk[:, :-1]) / dr

#   # cell quantities in FVM
#   A = 4 * np.pi * cell_R**2 # shape (n_r-1,)
#   V = A * dr # shape (n_r-1,)
#   cell_h = np.sum(scalar[1:] * hk, axis=0) # shape (n_r-1,)
#   cell_Jk = - rho * scalar[1:] * Dk * grad_Xk
#   cell_Jk -= scalar[1:] * np.sum(cell_Jk, axis=0) # shape (n_r-1,)
#   source_species = mdotk * V # shape (n_species, n_r-1), kg/s

#   # face quantities in FVM
#   face_A = 4 * np.pi * R**2 # shape (n_r,)
#   face_h = (cell_h[:-1] + cell_h[1:]) / 2 # shape (n_r-2,)
#   face_Yk = (scalar[1:, 1:] + scalar[1:, :-1]) / 2 # shape (n_species, n_r-2,), kg/kg
#   # face M
#   face_rho = (rho[1:] + rho[:-1]) / 2 # shape (n_r-2,)
#   face_u = - ((mdotk[:, 1:] + mdotk[:, :-1]) / 2).sum(axis=0) / face_rho  # shape (n_r-2,)
#   face_M = face_A[1:-1] * face_rho * face_u # shape (n_r-2,)
#   # face J
#   face_Jk = (cell_Jk[:, 1:] + cell_Jk[:, :-1]) / 2 # shape (n_species, n_r-2,), kg/m2/s
#   face_Jk -= face_Yk * np.sum(face_Jk, axis=0) # shape (n_species, n_r-2,), kg/m2/s
#   # face q
#   face_hk = (hk[:, 1:] + hk[:, :-1]) / 2 # shape (n_species, n_r-2,)
#   face_lam = (lam[1:] + lam[:-1]) / 2 # shape (n_r-2,)
#   face_grad_T = (scalar[0, 1:] - scalar[0, :-1]) / dr # shape (n_r-2,)
#   face_q = np.sum(face_hk * face_Jk, axis=0) - face_lam * face_grad_T # shape (n_r-2,), J/m2/s

#   # surface bc
#   # surf Y
#   omega = 0.853 # kg/m^2/s, surface mass flux from experiment
#   omega_k = np.zeros((gas.n_species))
#   omega_k[0] = 1.0
#   temp = rho[0] * Dk[:, 0] / (0.5 * dr)
#   Yk_s = (omega_k * omega + temp * scalar[1:, 0]) / (omega + temp)
#   # surf T
#   surf_T = scalar[0, 0] - (omega * 10.9e6 * .5 * dr / lam[-1]) # J/kg = m2/s2
#   # surf gas
#   surf_g = ct.Solution("./mechs/Al_gas.yaml")
#   surf_g.TPY = surf_T, ct.one_atm, Yk_s
#   # surf h, M
#   surf_h = surf_g.h # J/kg = m2/s2
#   surf_M = (4 * np.pi * R[0]**2) * omega # kg / s
#   # surf J
#   surf_rho = surf_g.density
#   surf_Dk = surf_g.mix_diff_coeffs
#   surf_grad_X = (Xk[:, 0] - surf_g.X) / (.5 * dr)
#   surf_Jk = -surf_rho * Yk_s * surf_Dk * surf_grad_X
#   surf_Jk -= Yk_s * np.sum(surf_Jk, axis=0) # kg/m2/s
#   # surf q
#   surf_lam = surf_g.thermal_conductivity
#   surf_hk = surf_g.partial_molar_enthalpies / surf_g.molecular_weights
#   surf_grad_T = (scalar[0, 0] - surf_g.T) / (.5 * dr)
#   surf_q = np.sum(surf_hk * surf_Jk, axis=0) - surf_lam * surf_grad_T # kg/s3

#   # far field bc
#   # far T, Y
#   far_grad_T = 0.0
#   far_grad_X = 0.0
#   farT = scalar[0, -1] + far_grad_T * (.5 * dr)
#   farX = Xk[:, -1] + far_grad_X * (.5 * dr)
#   # farT = far_T
#   # farX = scalar0[1:, 0]
#   # far gas
#   far_g = ct.Solution("./mechs/Al_gas.yaml")
#   far_g.TPX = farT, ct.one_atm, farX
#   # far h, M
#   far_h = far_g.h
#   far_M = 0.0
#   # far J
#   far_rho = far_g.density
#   far_Dk = far_g.mix_diff_coeffs
#   far_grad_X = (far_g.X - Xk[:, -1]) / (.5 * dr)
#   far_Jk = - far_rho * far_g.Y * far_Dk * far_grad_X
#   far_Jk -= far_g.Y * np.sum(far_Jk, axis=0)\
#   # far q
#   far_lam = far_g.thermal_conductivity
#   far_hk = far_g.partial_molar_enthalpies / far_g.molecular_weights
#   far_grad_T = (far_g.T - scalar[0, -1]) / (.5 * dr)
#   far_q = np.sum(far_hk * far_Jk, axis=0) - far_lam * far_grad_T

#   # concatenate boundary fluxes
#   face_Yk = np.insert(np.insert(face_Yk, 0, Yk_s, axis=-1), -1, far_g.Y, axis=-1) # kg/kg
#   face_Jk = np.insert(np.insert(face_Jk, 0, surf_Jk, axis=-1), -1, far_Jk, axis=-1) # kg/m2/s
#   face_h = np.insert(np.insert(face_h, 0, surf_h), -1, far_h) # J/kg
#   face_q = np.insert(np.insert(face_q, 0, surf_q), -1, far_q) # J/m2/s
#   face_M = np.insert(np.insert(face_M, 0, surf_M), -1, far_M) # kg/s

#   return (
#       rho * V * cell_h,     # shape (n_r-1,)
#       scalar[1:] * rho * V, # shape (n_species, n_r-1)
#       rho * V,              # shape (n_r-1,)
#       source_species,       # shape (n_species, n_r-1)
#       -np.sum(mdotk, axis=0) / rho, # shape (n_r-1,)
#       (face_M[1:] * face_h[1:] - face_M[:-1] * face_h[:-1]) + (
#         face_A[1:] * face_q[1:] - face_A[:-1] * face_q[:-1]
#       ),                    # shape (n_r-1,)
#       (face_M[1:] * face_Yk[:, 1:] - face_M[:-1] * face_Yk[:, :-1]) + (
#         face_A[1:] * face_Jk[:, 1:] - face_A[:-1] * face_Jk[:, :-1]
#       ),                    # shape (n_species, n_r-1)
#       face_M[1:] - face_M[:-1], # shape (n_r-1,)
#   )


# def get_residuals(scalar_old, scalar_new):
#   V1, V2, V3, V4, V5, F1, F2, F3 = scalar2gas(scalar_new)
#   v1, v2, v3, v4, v5, f1, f2, f3 = scalar2gas(scalar_old)
#   dt = 1e-8
#   rT = (V1 - v1) / dt + 0.5 * (F1 + f1)                   # shape (n_r-1,)
#   rY = (V2 - v2) / dt + 0.5 * (F2 + f2) - 0.5 * (V4 + v4) # shape (n_species, n_r-1)
#   rM = (V3 - v3) / dt + 0.5 * (F3 + f3)                   # shape (n_r-1,)
#   residual = np.concatenate([rT[None, :], rY, rM[None, :]])

#   return residual


# # scalar_gas[0] = far_T # initial gas temperature

# # def get_residuals(scalar_old, scalar_new, dt=1e-6):
# #     """Calculate residuals between current and next state."""
# #     V1, V2, V3, V4, V5, F1, F2, F3 = scalar2gas(scalar_old)
# #     v1, v2, v3, v4, v5, f1, f2, f3 = scalar2gas(scalar_new)
    
# #     # Residuals for conservation equations
# #     res_energy = (v1 - V1)/dt - F1
# #     res_species = (v2 - V2)/dt - F2 - V4
# #     res_mass = (v3 - V3)/dt - F3
    
# #     return np.vstack([res_energy, res_species, res_mass])


# def compute_jacobian(scalar, eps=1e-8):
#   """Compute Jacobian matrix analytically."""
#   n_vars = scalar.size
#   J = np.zeros(((scalar.shape[0] + 1) * scalar.shape[1], n_vars))
  
#   # Base residual
#   base_res = get_residuals(scalar, scalar).flatten()
  
#   # Compute Jacobian by finite difference
#   for i in range(n_vars):
#     scalar_perturb = scalar.copy()
#     idx = np.unravel_index(i, scalar.shape)
#     scalar_perturb[idx] += eps
#     perturb_res = get_residuals(scalar, scalar_perturb).flatten()
#     J[:, i] = (perturb_res - base_res) / eps
  
#   return J


# def newton_solver(scalar_init, max_iter=20, tol=1e-6):
#   """Newton-Raphson solver to find next state."""
#   scalar = scalar_init.copy()
  
#   for iter in range(max_iter):
#     res = get_residuals(scalar_gas, scalar).flatten()
#     res_norm = np.linalg.norm(res)
#     print(f"Iteration {iter}, residual norm: {res_norm:.6e}")
    
#     if res_norm < tol:
#         print("Converged!")
#         break
        
#     # Compute Jacobian and solve linear system
#     J = compute_jacobian(scalar)
#     delta = np.linalg.solve(J, -res)
    
#     # Update solution with line search
#     alpha = 1.0
#     scalar_new = scalar.copy()
#     scalar_new.flat += alpha * delta
    
#     # Ensure physical bounds
#     scalar_new[0] = np.clip(scalar_new[0], 300, 5000)  # Temperature bounds
#     scalar_new[1:] = np.clip(scalar_new[1:], 0, 1)     # Mass fraction bounds
#     scalar_new[1:] /= scalar_new[1:].sum(axis=0)       # Normalize mass fractions
      
#     scalar = scalar_new
    
#   return scalar


# # Time evolution and visualization
# def solve_time_evolution(scalar_init, n_steps=100, dt=1e-6):
#   """Solve time evolution from initial state and visualize results."""
#   scalar_history = [scalar_init.copy()]
#   time = [0.0]
  
#   # Solve for each time step
#   for step in range(n_steps):
#     print(f"Time step {step+1}/{n_steps}")
#     scalar_next = newton_solver(scalar_history[-1])
#     scalar_history.append(scalar_next)
#     time.append(time[-1] + dt)
  
#   # Convert to numpy arrays for easier indexing
#   scalar_history = np.array(scalar_history)
#   time = np.array(time)
  
#   # Visualize results
#   fig, axes = plt.subplots(2, 1, figsize=(10, 12))
  
#   # Plot temperature evolution
#   axes[0].plot(cell_R * 1e6, scalar_history[0, 0], 'b--', label='Initial')
#   axes[0].plot(cell_R * 1e6, scalar_history[-1, 0], 'r-', label='Final')
#   axes[0].set_xlabel('Radius (μm)')
#   axes[0].set_ylabel('Temperature (K)')
#   axes[0].set_title('Temperature Evolution')
#   axes[0].legend()
#   axes[0].grid(True)
  
#   # Plot species evolution (final state)
#   for i in range(1, gas.n_species + 1):
#       if np.max(scalar_history[-1, i]) > 1e-3:  # Only plot significant species
#           axes[1].plot(cell_R * 1e6, scalar_history[-1, i], label=f'{gas.species_names[i-1]}')
  
#   axes[1].set_xlabel('Radius (μm)')
#   axes[1].set_ylabel('Mass Fraction')
#   axes[1].set_title('Species Distribution (Final State)')
#   axes[1].legend()
#   axes[1].grid(True)
  
#   plt.tight_layout()
#   plt.savefig('time_evolution.png', dpi=300)
#   plt.show()
  
#   # Create animation of temperature evolution
#   fig, ax = plt.subplots(figsize=(8, 6))
#   line, = ax.plot(cell_R * 1e6, scalar_history[0, 0])
#   ax.set_xlabel('Radius (μm)')
#   ax.set_ylabel('Temperature (K)')
#   ax.set_title('Temperature Evolution')
#   ax.grid(True)
  
#   def update(frame):
#     line.set_ydata(scalar_history[frame, 0])
#     ax.set_title(f'Temperature at t = {time[frame]*1e6:.2f} μs')
#     return line,
  
#   from matplotlib.animation import FuncAnimation
#   ani = FuncAnimation(fig, update, frames=len(time), interval=100, blit=True)
#   ani.save('temperature_evolution.gif', writer='pillow', fps=10)
  
#   return scalar_history, time

# # Run the simulation
# final_states, simulation_time = solve_time_evolution(scalar_gas)



# out = scalar2gas(scalar_gas)


def steady_model(
  t, 
  scalar,
  pressure=ct.one_atm
):
  """1D steady state model.
  Args:
    scalar: numpy.ndarray, (nspecies+1, n_x)
  """
  # scalars in cells
  scalar = rearrange(scalar, '(s d) -> s d', d=cell_R.shape[0])
  scalar = scalar.reshape(-1, cell_R.shape[0])
  scalar[0] = np.clip(scalar[0], a_min=300, a_max=5000)
  scalar[1:] = np.clip(scalar[1:], a_min=0, a_max=None)
  scalar[1:] /= scalar[1:].sum(axis=0)

  # thermochemical properties in cells
  gas_array = ct.SolutionArray(gas, shape=cell_R.shape[0])
  gas_array.TPY = scalar[0], pressure, scalar[1:].T
  Xk = gas_array.X.T # kmol/kmol
  Dk = gas_array.mix_diff_coeffs.T # m2/s
  rho = gas_array.density # kg/m3
  cp = gas_array.cp # J/kg/K
  lam = gas_array.thermal_conductivity # W/m/K = kg*m2/s2/s/m/K
  hk = (gas_array.partial_molar_enthalpies / gas_array.molecular_weights).T # J/kg = m2/s2
  qdot = gas_array.heat_release_rate # W/m3 = J/s/m3 = kg/m/s3
  mdotk = (gas_array.net_production_rates * gas_array.molecular_weights).T # kg/m3/s
  grad_Xk = np.zeros_like(Xk) # kmol/kmol/m
  grad_Xk[:, :-1] = (Xk[:, 1:] - Xk[:, :-1]) / dr

  # cell quantities in FVM
  cell_h = np.sum(scalar[1:] * hk, axis=0)        # shape (n_r-1,), J/kg
  cell_Jk = - rho * scalar[1:] * Dk * grad_Xk
  cell_Jk -= scalar[1:] * np.sum(cell_Jk, axis=0) # shape (n_species, n_r-1), kg/m2/s
  V = (4 * np.pi * cell_R**2) * dr                # shape (n_r-1,), m3
  source_species = mdotk * V                      # shape (n_species, n_r-1), kg/s
  # source_heat = qdot * V # J/s
  
  # face quantities in FVM
  face_A = 4 * np.pi * R**2                                                     # shape (n_r,)
  face_h = (cell_h[:-1] + cell_h[1:]) / 2                                       # shape (n_r-2,)
  face_Yk = (scalar[1:, 1:] + scalar[1:, :-1]) / 2                              # shape (n_species, n_r-2), kg/kg
  # face M
  face_rho = (rho[1:] + rho[:-1]) / 2                                           # shape (n_r-2,)
  face_u = - ((mdotk[:, 1:] + mdotk[:, :-1]) / 2).sum(axis=0) / face_rho        # shape (n_r-2,)
  face_M = face_A[1:-1] * face_rho * face_u                                     # shape (n_r-2,)
  # face J
  face_Jk = (cell_Jk[:, 1:] + cell_Jk[:, :-1]) / 2                              # shape (n_species, n_r-2), kg/m2/s
  face_Jk -= face_Yk * np.sum(face_Jk, axis=0)                                  # shape (n_species, n_r-2), kg/m2/s
  # face q
  face_hk = (hk[:, 1:] + hk[:, :-1]) / 2                                        # shape (n_species, n_r-2), J/kg
  face_lam = (lam[1:] + lam[:-1]) / 2                                           # shape (n_r-2,), J/m/s/K
  face_grad_T = (scalar[0, 1:] - scalar[0, :-1]) / dr                           # shape (n_r-2,), K/m
  face_q = np.sum(face_hk * face_Jk, axis=0) - face_lam * face_grad_T           # shape (n_r-2,), J/m2/s

  # # face quantities in FVM
  # face_hk = (hk[:, 1:] + hk[:, :-1]) / 2
  # face_lam = (lam[1:] + lam[:-1]) / 2
  # face_grad_T = (scalar[0, 1:] - scalar[0, :-1]) / dr

  # # face fluxes
  # face_A = 4 * np.pi * R**2
  # face_Yk = (scalar[1:, 1:] + scalar[1:, :-1]) / 2 # kg/kg
  # face_Jk = (cell_Jk[:, 1:] + cell_Jk[:, :-1]) / 2 # kg/m2/s
  # face_Jk -= face_Yk * np.sum(face_Jk, axis=0)
  # face_h = (cell_h[:-1] + cell_h[1:]) / 2
  # face_q = np.sum(face_hk * face_Jk, axis=0) - face_lam * face_grad_T # J/m2/s

  # surface bc
  # surf Y
  omega = 0.853                                                                 # kg/m2/s, surface mass flux from experiment
  omega_k = np.zeros((gas.n_species))
  omega_k[0] = 1.0
  temp = rho[0] * Dk[:, 0] / (0.5 * dr)
  Yk_s = (omega_k * omega + temp * scalar[1:, 0]) / (omega + temp)
  # surf T
  surf_T = scalar[0, 0] - (omega * 10.9e6 * .5 * dr / lam[-1])                  # -\lambda \nabla T = \omega H_{l2g}
  # surf_T = 2650
  # scalar[0, 0] = surf_T + (omega * 10.9e6 * .5 * dr / lam[-1])
  # surf gas
  surf_g = ct.Solution("./mechs/Al_gas.yaml")
  surf_g.TPY = surf_T, ct.one_atm, Yk_s
  # surf h, M
  surf_h = surf_g.h                                                             # J/kg = m2/s2
  surf_M = (4 * np.pi * R[0]**2) * omega                                        # kg / s
  # surf J
  surf_rho = surf_g.density
  surf_Dk = surf_g.mix_diff_coeffs
  surf_grad_X = (Xk[:, 0] - surf_g.X) / (.5 * dr)
  # surf_Jk = -surf_rho * Yk_s * surf_Dk * surf_grad_X
  # surf_Jk -= Yk_s * np.sum(surf_Jk, axis=0)                                     # kg/m2/s
  surf_Jk = -omega * Yk_s
  surf_Jk[0] = omega * (1 - Yk_s[0])
  # surf q
  surf_lam = surf_g.thermal_conductivity
  surf_hk = surf_g.partial_molar_enthalpies / surf_g.molecular_weights
  surf_grad_T = (scalar[0, 0] - surf_g.T) / (.5 * dr)
  surf_q = np.sum(surf_hk * surf_Jk, axis=0) - surf_lam * surf_grad_T           # J/m2/s = kg/s3

  # # approximate rho and D with first cell quantities
  # omega_k = np.zeros((gas.n_species))
  # omega_k[0] = 1.0
  # temp = rho[0] * Dk[:, 0] / (0.5 * dr)
  # Yk_s = (omega_k * omega + temp * scalar[1:, 0]) / (omega + temp)
  # # Yk_s /= Yk_s.sum()

  # surf_T = scalar[0, 0] - (omega * 10.9e6 * .5 * dr / lam[-1]) # J/kg = m2/s2
  # surf_g = ct.Solution("./mechs/Al_gas.yaml")
  # surf_g.TPY = surf_T, pressure, Yk_s
  # surf_Dk = surf_g.mix_diff_coeffs
  # surf_hk = surf_g.partial_molar_enthalpies / surf_g.molecular_weights
  # surf_rho = surf_g.density
  # surf_lam = surf_g.thermal_conductivity
  # surf_grad_X = (Xk[:, 0] - surf_g.X) / (.5 * dr)
  # surf_grad_T = (scalar[0, 0] - surf_g.T) / (.5 * dr)

  # surf_Jk = -surf_rho * Yk_s * surf_Dk * surf_grad_X
  # surf_Jk -= Yk_s * np.sum(surf_Jk, axis=0) # kg/m2/s
  # surf_h = surf_g.h # J/kg = m2/s2
  # surf_q = np.sum(surf_hk * surf_Jk, axis=0) - surf_lam * surf_grad_T # kg/s3
  
  # far field bc
  # far T, Y
  far_grad_T = 0.0
  far_grad_X = 0.0
  farT = scalar[0, -1] + far_grad_T * (.5 * dr)
  farX = Xk[:, -1] + far_grad_X * (.5 * dr)
  # farT = far_T
  # farX = scalar0[1:, 0]
  # far gas
  far_g = ct.Solution("./mechs/Al_gas.yaml")
  far_g.TPX = farT, ct.one_atm, farX
  # far h, M
  far_h = far_g.h
  far_rho = far_g.density
  # far_u= 
  far_M = 0.0
  # far J
  far_Dk = far_g.mix_diff_coeffs
  far_grad_X = (far_g.X - Xk[:, -1]) / (.5 * dr)
  far_Jk = - far_rho * far_g.Y * far_Dk * far_grad_X
  far_Jk -= far_g.Y * np.sum(far_Jk, axis=0)\
  # far q
  far_lam = far_g.thermal_conductivity
  far_hk = far_g.partial_molar_enthalpies / far_g.molecular_weights
  far_grad_T = (far_g.T - scalar[0, -1]) / (.5 * dr)
  far_q = np.sum(far_hk * far_Jk, axis=0) - far_lam * far_grad_T

  # # far-field bc
  # far_grad_T = 0.0 # (far_g.T - scalar[0, -1]) / (.5 * dr)
  # far_grad_X = 0.0
  # farT = scalar[0, -1] + far_grad_T * (.5 * dr)
  # farX = Xk[:, -1] + far_grad_X * (.5 * dr)
  # # farT = far_T
  # # farX = scalar0[1:, 0]
  # far_g = ct.Solution("./mechs/Al_gas.yaml")
  # far_g.TPX = farT, pressure, farX
  # far_Dk = far_g.mix_diff_coeffs
  # far_hk = far_g.partial_molar_enthalpies / far_g.molecular_weights
  # far_rho = far_g.density
  # far_lam = far_g.thermal_conductivity
  # far_grad_X = (far_g.X - Xk[:, -1]) / (.5 * dr)
  # far_grad_T = (far_g.T - scalar[0, -1]) / (.5 * dr)

  # far_Jk = - far_rho * far_g.Y * far_Dk * far_grad_X
  # far_Jk -= far_g.Y * np.sum(far_Jk, axis=0)
  # far_h = far_g.h
  # far_q = np.sum(far_hk * far_Jk, axis=0) - far_lam * far_grad_T

  # concatenate boundary fluxes
  face_Yk = np.insert(
      np.insert(face_Yk, 0, Yk_s, axis=-1), -1, far_g.Y, axis=-1)               # shape (n_species, n_r), kg/kg
  face_Jk = np.insert(
      np.insert(face_Jk, 0, surf_Jk, axis=-1), -1, far_Jk, axis=-1)             # shape (n_species, n_r), kg/m2/s
  face_h = np.insert(np.insert(face_h, 0, surf_h), -1, far_h)                   # shape (n_r), J/kg
  face_q = np.insert(np.insert(face_q, 0, surf_q), -1, far_q)                   # shape (n_r), J/m2/s
  # face_M = np.insert(np.insert(face_M, 0, surf_M), -1, far_M)                   # shape (n_r), kg/s
  face_M = np.ones_like(face_A) * surf_M

  # compute vector fields
  pm_pt = -(face_M[1:] - face_M[:-1])                                           # shape (n_r-1), kg/s
  pT_pt = 1 / rho / V / cp * (
      -(face_M[1:] * face_h[1:] - face_M[:-1] * face_h[:-1]) - (
        face_q[1:] * face_A[1:] - face_q[:-1] * face_A[:-1]) - (
        pm_pt * cell_h))                                                        # shape (n_species, n_r-1), T/s
  pYk_pt = 1 / rho / V * (
      -(face_M[1:] * face_Yk[:, 1:] - face_M[:-1] * face_Yk[:, :-1]) - (
        face_Jk[:, 1:] * face_A[1:] - face_Jk[:, :-1] * face_A[:-1]) + (
        source_species) - (pm_pt * scalar[1:]))                                 # shape (n_species, n_r-1), kg/s
  out = np.insert(pYk_pt, 0, pT_pt, axis=0)
  out = rearrange(out, 's d -> (s d)')                                          # shape (n_species+1, n_r-1)
  return out



  # # residual for governing euqations
  # rate_Yk = 1 / rho / V * (
  #   source_species 
  #     - M * (face_Yk[:, 1:] - face_Yk[:, :-1]) - (
  #     face_Jk[:, 1:] * face_A[1:] - face_Jk[:, :-1] * face_A[:-1]
  #   )
  # )
  # rate_T = 1 / rho / V / cp * (
  #   # source_heat 
  #     - M * (face_h[1:] - face_h[:-1]) - (
  #     face_q[1:] * face_A[1:] - face_q[:-1] * face_A[:-1]
  #   )
  # )

  # out = np.insert(rate_Yk, 0, rate_T, axis=0)
  # out = rearrange(out, 's d -> (s d)')

  # return out # np.insert(rate_Yk, 0, rate_T, axis=0).flatten()


def plot_func(cell_array_list):
  fig = plt.figure()
  ax1 = fig.add_subplot(111)
  colors = ['r', 'b', 'g', 'c']
  lines = ['-', '--', '-.', ':']
  for i, array in enumerate(cell_array_list):
    ax1.plot(cell_R[:1238], array[:1238], c=colors[i], ls=lines[i], label=f'line{i:d}')
  plt.legend()
  plt.savefig('test.svg', bbox_inches='tight')
  plt.show()
  plt.close()

plot_idx = 0

def event_fn(t, y):
  """log and plot the temperature field."""
  y = rearrange(y, '(s d) -> s d', d=cell_R.shape[0])
  print(f"Checkpoint: t = {t:.6e}, "
        f"T = [{', '.join(f'{x:7.2f}' for x in y[0, 0:-1:100])}]")

  # plt.figure(figsize=(6,4))
  global plot_idx
  if plot_idx % 100 == 0:
    fig, ax = plt.subplots(2, 2, figsize=(12, 8))
    ax[0, 0].plot(cell_R / r_min, y[0])
    ax[0, 0].set_xlim([1.0, 15.0])
    ax[0, 0].set_ylim([2500, 4500])
    ax[0, 0].set_xlabel(r'$r/r_p$')
    ax[0, 0].set_ylabel(r'$T$/(K)')
    ax[0, 1].plot(cell_R / r_min, y[2])
    ax[0, 1].set_xlim([1.0, 15.0])
    ax[0, 1].set_ylim([0., 1.])
    ax[0, 1].set_xlabel(r'$r/r_p$')
    ax[0, 1].set_ylabel(r'$\mathrm{AlO}$')
    ax[1, 0].plot(cell_R / r_min, y[-5])
    ax[1, 0].set_xlim([1.0, 15.0])
    ax[1, 0].set_ylim([0., 1.])
    ax[1, 0].set_xlabel(r'$r/r_p$')
    ax[1, 0].set_ylabel(r'$\mathrm{Al_2O_3(L)}$')
    ax[1, 1].plot(cell_R / r_min, y[1])
    ax[1, 1].set_xlim([1.0, 15.0])
    ax[1, 1].set_ylim([0., 1.])
    ax[1, 1].set_xlabel(r'$r/r_p$')
    ax[1, 1].set_ylabel('Al')
    plt.savefig('./al_temprature.pdf', bbox_inches='tight')
    plt.close()
    plt.clf() 
  plot_idx += 1

  return 0  # Trigger event logging

event_fn.terminal = False
event_fn.direction = 0

y0 = rearrange(scalar_gas, 's d -> (s d)')
solution = solve_ivp(
  steady_model,
  t_span=[0, 1e-4],
  y0=y0, # scalar0.flatten(), # x.flatten(),
  method='RK45',
  t_eval=np.arange(0, 1e-4, 1e-6),
  events=event_fn  # Attach event function
)
np.savez('./data/solution_arrays.npz', t=solution.t, y=solution.y)

fig = plt.figure()
ax1 = fig.add_subplot(111)
ax1.plot(solution.t * 1e6, solution.y[0], c='r', ls='-', label=r'$T$')
ax1.set_ylabel('Temperature')
ax1.set_xlabel(r'Time ($\mu$s)')
ax2 = ax1.twinx()  # this is the important function
ax2.plot(solution.t * 1e6, solution.y[0], c='g', ls=':', label='Al')
ax2.plot(solution.t * 1e6, solution.y[1], c='y', ls='-.', label='AlO')
ax2.plot(solution.t * 1e6, solution.y[7], c='c', ls='--', label='Al2O3(l)')
ax2.plot(solution.t * 1e6, solution.y[9], c='b', ls='-', label='O2')
ax2.set_ylabel('Mole fraction')

plt.savefig('test.svg', bbox_inches='tight')
plt.show()

