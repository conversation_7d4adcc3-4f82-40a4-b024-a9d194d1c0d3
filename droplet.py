# Parralelized 2D CFD simulation

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
config = {
	"font.family":'sans serif',
	"font.size": 12,
	"mathtext.fontset":'custom',
	"font.sans-serif": ['Arial'],
	"axes.unicode_minus":False,
}
rcParams.update(config)
labelsize = 12
from mpl_toolkits.axes_grid1.inset_locator import inset_axes

nu = 1.48e-5 # m2/s for air, 1.0  # Kinematic viscosity (example value)
f = 100.0 # frequency
omega = 2 * np.pi * f # Angular frequency (example value)
a = 21.0e-6  # Droplet radius (example value)
u0 = 0.2  # Reference velocity (example value)

# Computational domain parameters
num_r = 101  # Number of radial points
num_theta = 101  # Number of angular points
r_star_max = 10.0  # Maximum non-dimensional radius
r_star_min = 1.0  # Maximum non-dimensional radius
theta_star_max = 1.0 # np.pi  # Maximum angle (non-dimensional)
theta_star_min = 0.0 # np.pi  # Maximum angle (non-dimensional)

# Parameters
St = omega * a / u0  # Strouhal number
Pe = 2 * a * u0 / 5.6e-6  # Peclet number
dt_star = 0.5e-4  # Non-dimensional time step

# Initialize concentration field
C_i_star = np.zeros((num_r, num_theta))

# Initialize radial and angular grids
r_star = np.linspace(r_star_min, r_star_max, num_r)
dr_star = r_star[1] - r_star[0]
theta_star = np.linspace(theta_star_min, theta_star_max, num_theta)
dtheta_star = theta_star[1] - theta_star[0]
R_star, Theta_star = np.meshgrid(r_star, theta_star)

# Initial condition: C_i* = 0 at r* > 1, C_i* = 1 at r* = 1
C_i_star[1:, :] = 0.0
C_i_star[0, :] = 1.0
C_i_star_new = np.copy(C_i_star)

# Function to compute F(r) and G(r)
def compute_F_G(r_star, a, nu, omega):
	r = r_star * a # dimensional radius
	k = (1 + 1j) / np.sqrt(2 * nu / omega) # wavelength
	A = -3*a / (2 * 1j * k) * np.exp(1j * k * r)
	B = -a**3 / 2 * (1 - 3 / (1j * k * a) - 3 / (k**2 * a**2))
	F = (A * np.exp(1j * k * r) * (r - 1/(1j * k)) + B) / r**3
	G = (A * np.exp(1j * k * r) * (1j * k * r**2 - r + 1/(1j * k)) - B) / r**3
	return F, G

# Function to compute the radial and tangential velocity
def compute_velocity(r_star, theta_star, t_star, a, nu, omega):
  F, G = compute_F_G(r_star, a, nu, omega)
  v_r_star = -2 * np.exp(-1j * t_star) * F * np.sin(np.pi * theta_star)
  v_theta_star = np.exp(-1j * t_star) * G * np.sin(np.pi * theta_star)
  return v_r_star.real, v_theta_star.real

V_r_star = np.zeros_like(C_i_star)
V_theta_star = np.zeros_like(C_i_star)

# Time-stepping loop
for n in range(10001):  # Example number of time steps
	t_star = n * dt_star

	C_i_star_pad = np.pad(C_i_star, pad_width=((1, 1), (0, 0)), mode='edge')
	C_i_star_pad = np.pad(C_i_star_pad, pad_width=((0, 0), (2, 2)), mode='symmetric')

	V_r_star, V_theta_star = compute_velocity(R_star.T, Theta_star.T, t_star, a, nu, omega)

	# radial convective term
	convective_r_up = (V_r_star[1:num_r-1] - np.cos(t_star) * np.cos(np.pi * Theta_star[:, 1:num_r-1].T)) * (
		3 * C_i_star_pad[2:num_r, 2:num_theta+2] - 4 * C_i_star_pad[1:num_r-1, 2:num_theta+2] + C_i_star_pad[0:num_r-2, 2:num_theta+2]
	) / (2 * dr_star)
	convective_r_down = (V_r_star[1:num_r-1] - np.cos(t_star) * np.cos(np.pi * Theta_star[:, 1:num_r-1].T)) * (
		-3 * C_i_star_pad[2:num_r, 2:num_theta+2] + 4 * C_i_star_pad[3:num_r+1, 2:num_theta+2] - C_i_star_pad[4:num_r+2, 2:num_theta+2]
	) / (2 * dr_star)
	upwind = (V_r_star[1:num_r-1] - np.cos(t_star) * np.cos(np.pi * Theta_star[:, 1:num_r-1].T)) > 0
	convective_r = convective_r_up * upwind + convective_r_down * ~upwind

	# tangential convective term
	convective_theta_up = (V_theta_star[1:num_r-1] + np.cos(t_star) * np.sin(np.pi * Theta_star[:, 1:num_r-1].T)) * (
		3 * C_i_star_pad[2:num_r, 2:num_theta+2] - 4 * C_i_star_pad[2:num_r, 1:num_theta+1] + C_i_star_pad[2:num_r, 0:num_theta]
	) / (2 * dtheta_star)
	convective_theta_down = (V_theta_star[1:num_r-1] + np.cos(t_star) * np.sin(np.pi * Theta_star[:, 1:num_r-1].T)) * (
		-3 * C_i_star_pad[2:num_r, 2:num_theta+2] + 4 * C_i_star_pad[2:num_r, 3:num_theta+3] + C_i_star_pad[2:num_r, 4:num_theta+4]
	) / (2 * dtheta_star)
	upwind = (V_theta_star[1:num_r-1] + np.cos(t_star) * np.sin(np.pi * Theta_star[:, 1:num_r-1].T)) > 0
	convective_theta = convective_theta_up * upwind + convective_theta_down * ~upwind

	# diffusive terms
	diffusive_r = (C_i_star_pad[3:num_r+1, 2:num_theta+2] - 2 * C_i_star_pad[2:num_r, 2:num_theta+2] + C_i_star_pad[1:num_r-1, 2:num_theta+2]) / dr_star**2
	diffusive_r_additional = (2 / R_star[:, 1:num_r-1].T) * (C_i_star_pad[3:num_r+1, 2:num_theta+2] - C_i_star_pad[1:num_r-1, 2:num_theta+2]) / (2 * dr_star)

	# Time derivative from mass transfer equation
	time_derivative = (
		- convective_r - convective_theta / (R_star[:, 1:num_r-1].T * np.pi)
		+ (2 / Pe) * (diffusive_r + diffusive_r_additional)
	) / St

	# Update concentration field
	C_i_star_new[1:num_r-1, :] = C_i_star[1:num_r-1, :] + dt_star * time_derivative
	C_i_star_new[0, :] = 1.0
	C_i_star_new[num_r-1, :] = 0.0

	# Update concentration field for next time step
	C_i_star = np.copy(np.clip(C_i_star_new, a_min=0, a_max=1))

	# Output the final concentration field
	fig, ax = plt.subplots(figsize=(7,5), subplot_kw={'projection': 'polar'})
	c = ax.pcolormesh(Theta_star * np.pi, R_star, C_i_star.T, shading='auto', cmap='viridis')
	# Add a colorbar
	cax = inset_axes(ax, width="5%", height="70%", loc='right', bbox_to_anchor=(0.2, 0, 1, 1), bbox_transform=ax.transAxes)
	cbar = plt.colorbar(c, cax=cax)
	cbar.set_label(r'$C_i*$')
	# Set the angular limits to show only the upper half-circle
	ax.set_thetamax(180)  # Limit theta to 0 to 180 degrees
	ax.set_title(f"Mass concentration field", y=0.88)
	ax.set_xlabel(r'$\theta *$')
	ax.set_ylabel(r'$r*$')
	ax.xaxis.set_label_coords(0.5, 0.15)  # 调整坐标值，使 ylabel 更靠近主图
	# Show the plot
	if n % 100 == 0:
		print(f"Time step {n} completed")
		plt.savefig(f'./simfigs/field_{n:d}.jpg', bbox_inches='tight')
	plt.close()