
"""Use <PERSON><PERSON><PERSON> method to solve the steady state problem."""


"""1D steady flow model for aluminum droplet combustion."""
import numpy as np
import cantera as ct
from scipy import optimize

from scipy.optimize import minimize, LinearConstraint, Bounds

import matplotlib.pyplot as plt
from matplotlib import rcParams
config = {
	"font.family":'sans serif',
	"font.size": 12,
	"mathtext.fontset":'custom',
	"font.sans-serif": ['Arial'],
	"axes.unicode_minus":False,
}
rcParams.update(config)
labelsize = 12

# Define the dimensions of x
n_rows = 13
n_cols = 734
# T_max = 4000

# Reshape x to a 2D matrix
def reshape_x(x):
    return x.reshape((n_rows, n_cols))

# Define the objective function for scipy
def objective_function(x):
    # x_2d = reshape_x(x)
    return steady_model(x)

# Define the bounds
# First row: [2500, 4000], remaining rows: [0, 1]
bounds = Bounds(
  lb=[2400] * n_cols + [0] * ((n_rows - 1) * n_cols),
  ub=[5000] * n_cols + [1] * ((n_rows - 1) * n_cols)
)

# Define the constraint for the sum of rows 1 to 12
# The sum of x[1:, :] along axis=0 must equal 1 for each column
A = np.zeros((n_cols, n_rows * n_cols))
for i in range(n_cols):
  A[i, i + n_cols::n_cols] = 1  # Select rows 1 to 12 for each column

linear_constraint = LinearConstraint(A, lb=np.ones(n_cols), ub=np.ones(n_cols))

A_last_row = np.zeros((n_cols, n_rows * n_cols))
for i in range(n_cols):
  A_last_row[i, (n_rows - 1) * n_cols + i] = 1  # Select the last row for each column
last_row_constraint = LinearConstraint(A_last_row, lb=np.zeros(n_cols), ub=np.zeros(n_cols))


################################################################################

# computational domain
n_r = 735
r_min = 105e-6
r_max = r_min * 15
r = np.linspace(r_min, r_max, n_r)
dr = r[1] - r[0]
R = np.meshgrid(r)[0]
cell_R = (R[1:] + R[:-1]) / 2

# initial scalar field
far_T = 300
surf_T = 2650
gas = ct.Solution("./mechs/Al_gas.yaml")
gas.TPX = far_T, ct.one_atm, {'O2':0.21, 'AR':0.79}
scalar0 = np.zeros((gas.n_species + 1, cell_R.shape[0])) # T,P,Y
# f = lambda x: 2717.745 + 12.35727 * x - 0.04548113 * x*x + 0.00005990919 * x*x*x - 2.713432e-8 * x*x*x*x
# scalar0[0] = f(np.arange(cell_R.shape[0])/1.26+1)
scalar0[0] = far_T # initial temperature
# scalar0[1] = 0.8**((np.arange(cell_R.shape[0])+1) ** (1/2.5))
scalar0[-3] = (1 - scalar0[1]) * gas.Y[-3] # Y of oxygen
scalar0[-2] = (1 - scalar0[1]) * gas.Y[-2] # Y of argon
scalar0[1:] /= scalar0[1:].sum(axis=0) 

print(R.shape, scalar0.shape, scalar0[-3][0])


def steady_model(scalar, pressure=ct.one_atm):
  """1D steady state model.
  Args:
    scalar: numpy.ndarray, (n_species + 1, n_x)
  """
  # thermochemical properties in cells
  scalar = reshape_x(scalar)
  gas_array = ct.SolutionArray(gas, shape=cell_R.shape[0])
  gas_array.TPY = scalar[0], pressure, scalar[1:].T
  Xk = gas_array.X.T
  Dk = gas_array.mix_diff_coeffs.T
  rho = gas_array.density
  cp = gas_array.cp # J/kg/K
  lam = gas_array.thermal_conductivity
  hk = (gas_array.partial_molar_enthalpies / gas_array.molecular_weights).T
  qdot = gas_array.heat_release_rate # W/m3 = J/s/m3 = kg/m/s3
  mdotk = (gas_array.net_production_rates * gas_array.molecular_weights).T
  grad_Xk = np.zeros_like(Xk)
  grad_Xk[:, :-1] = (Xk[:, 1:] - Xk[:, :-1]) / dr # kmol/kmol/m

  # cell quantities in FVM
  cell_h = np.sum(scalar[1:] * hk, axis=0)
  cell_Jk = - rho * scalar[1:] * Dk * grad_Xk
  cell_Jk -= scalar[1:] * np.sum(cell_Jk, axis=0)

  # source terms
  A = 4 * np.pi * cell_R**2
  V = A * dr
  spe_source_term = mdotk * V

  # boundary residual
  omega = 0.853 # kg/m^2/s, from experiment

  # approximate rho and D with first cell quantities
  omega_k = np.zeros((gas.n_species))
  omega_k[0] = 1.0
  temp = rho[0] * Dk[:, 0] / (0.5 * dr)
  Yk_s = (omega_k + temp * scalar[1:, 0]) / (omega + temp)
  Yk_s /= Yk_s.sum()

  # face quantities in FVM
  face_hk = (hk[:, 1:] + hk[:, :-1]) / 2
  face_Jk = (cell_Jk[:, 1:] + cell_Jk[:, :-1]) / 2
  face_lam = (lam[1:] + lam[:-1]) / 2
  face_grad_T = (scalar[0, 1:] - scalar[0, :-1]) / dr

  # face fluxes
  face_A = 4 * np.pi * R**2
  face_h = (cell_h[:-1] + cell_h[1:]) / 2
  face_q = np.sum(face_hk * face_Jk, axis=0) - face_lam * face_grad_T
  face_Yk = (scalar[1:, 1:] + scalar[1:, :-1]) / 2

  # add surface boundary
  surf_T = scalar[0, 0] - (omega * 293.4e6 / gas_array.mean_molecular_weight[-1] * .5 * dr / lam[-1]) # J/kg = m2/s2
  surf_g = ct.Solution("./mechs/Al_gas.yaml")
  surf_g.TPY = surf_T, pressure, Yk_s
  surf_Dk = surf_g.mix_diff_coeffs
  surf_hk = surf_g.partial_molar_enthalpies / surf_g.molecular_weights
  surf_rho = surf_g.density
  surf_lam = surf_g.thermal_conductivity
  surf_grad_X = (Xk[:, 0] - surf_g.X) / (.5 * dr)
  surf_grad_T = (scalar[0, 0] - surf_g.T) / (.5 * dr)

  surf_h = surf_g.h # J/kg = m2/s2
  surf_Jk = -surf_rho * Yk_s * surf_Dk * surf_grad_X
  surf_Jk -= Yk_s * np.sum(surf_Jk, axis=0) # kg/m2/s
  surf_q = np.sum(surf_hk * surf_Jk, axis=0) - surf_lam * surf_grad_T # kg/s3

  # add far-field boundary
  far_g = ct.Solution("./mechs/Al_gas.yaml")
  far_g.TPY = far_T, pressure, scalar0[1:, -1]
  far_Dk = far_g.mix_diff_coeffs
  far_hk = far_g.partial_molar_enthalpies / far_g.molecular_weights
  far_rho = far_g.density
  far_lam = far_g.thermal_conductivity
  far_grad_X = (far_g.X - Xk[:, -1]) / (.5 * dr)
  far_grad_T = (far_g.T - scalar[0, -1]) / (.5 * dr)

  far_h = far_g.h
  far_Jk = - far_rho * far_g.Y * far_Dk * far_grad_X
  far_Jk -= far_g.Y * np.sum(far_Jk, axis=0)
  far_q = np.sum(far_hk * far_Jk, axis=0) - far_lam * far_grad_T

  # concatenate boundary fluxes
  face_h = np.insert(np.insert(face_h, 0, surf_h), -1, far_h)
  face_q = np.insert(np.insert(face_q, 0, surf_q), -1, far_q)
  face_Yk = np.insert(np.insert(face_Yk, 0, Yk_s, axis=-1), -1, far_g.Y, axis=-1)
  face_Jk = np.insert(np.insert(face_Jk, 0, surf_Jk, axis=-1), -1, far_Jk, axis=-1)

  # residual for governing euqations
  M = (4 * np.pi * R[0]**2) * omega
  res_energy = M * (face_h[1:] * face_A[1:] - face_h[:-1] * face_A[:-1]) # kg/m4/s3
  res_energy += face_q[1:] * face_A[1:]**2 - face_q[:-1] * face_A[:-1]**2
  res_species = M * (face_Yk[:, 1:] * face_A[1:] - face_Yk[:, :-1] * face_A[:-1]) # kg/m2/s
  res_species += face_Jk[:, 1:] * face_A[1:]**2 - face_Jk[:, :-1] * face_A[:-1]**2
  res_species -= spe_source_term

  # res = np.insert(res_species / res_species.max(), 0, res_energy / res_energy.max(), axis=0)
  res = np.insert(res_species, 0, res_energy, axis=0)

  return res.flatten()


# iteration = 0
# def callback_function(y):
#   global iteration
#   print(f"Iteration {iteration}: temperature {y[0, ::100]}.")

#   if iteration % 100 == 0:
#     fig, ax = plt.subplots(2, 2, figsize=(12, 8))
#     ax[0, 0].plot(cell_R / r_min, y[0])
#     ax[0, 0].set_xlim([1.0, 15.0])
#     ax[0, 0].set_ylim([2500, 4500])
#     ax[0, 0].set_xlabel(r'$r/r_p$')
#     ax[0, 0].set_ylabel(r'$T$/(K)')
#     ax[0, 1].plot(cell_R / r_min, y[2])
#     ax[0, 1].set_xlim([1.0, 15.0])
#     ax[0, 1].set_ylim([0., 1.])
#     ax[0, 1].set_xlabel(r'$r/r_p$')
#     ax[0, 1].set_ylabel(r'$\mathrm{AlO}$')
#     ax[1, 0].plot(cell_R / r_min, y[-5])
#     ax[1, 0].set_xlim([1.0, 15.0])
#     ax[1, 0].set_ylim([0., 1.])
#     ax[1, 0].set_xlabel(r'$r/r_p$')
#     ax[1, 0].set_ylabel(r'$\mathrm{Al_2O_3(L)}$')
#     ax[1, 1].plot(cell_R / r_min, y[1])
#     ax[1, 1].set_xlim([1.0, 15.0])
#     ax[1, 1].set_ylim([0., 1.])
#     ax[1, 1].set_xlabel(r'$r/r_p$')
#     ax[1, 1].set_ylabel('Al')
#     plt.savefig('./al_temprature.pdf', bbox_inches='tight')
#     plt.close()
#     plt.clf() 
#   iteration += 1

#   return 0  # Trigger event logging

def plot_func(y):
  y = reshape_x(y)
  print(f"Temperature {y[0, ::100]}.")
  fig, ax = plt.subplots(2, 2, figsize=(12, 8))
  ax[0, 0].plot(cell_R / r_min, y[0])
  ax[0, 0].set_xlim([1.0, 15.0])
  ax[0, 0].set_ylim([2500, 4500])
  ax[0, 0].set_xlabel(r'$r/r_p$')
  ax[0, 0].set_ylabel(r'$T$/(K)')
  ax[0, 1].plot(cell_R / r_min, y[2])
  ax[0, 1].set_xlim([1.0, 15.0])
  ax[0, 1].set_ylim([0., 1.])
  ax[0, 1].set_xlabel(r'$r/r_p$')
  ax[0, 1].set_ylabel(r'$\mathrm{AlO}$')
  ax[1, 0].plot(cell_R / r_min, y[-5])
  ax[1, 0].set_xlim([1.0, 15.0])
  ax[1, 0].set_ylim([0., 1.])
  ax[1, 0].set_xlabel(r'$r/r_p$')
  ax[1, 0].set_ylabel(r'$\mathrm{Al_2O_3(L)}$')
  ax[1, 1].plot(cell_R / r_min, y[1])
  ax[1, 1].set_xlim([1.0, 15.0])
  ax[1, 1].set_ylim([0., 1.])
  ax[1, 1].set_xlabel(r'$r/r_p$')
  ax[1, 1].set_ylabel('Al')
  plt.savefig('./al_temprature.pdf', bbox_inches='tight')
  plt.close()
  plt.clf() 



# Solve the optimization problem
# result = minimize(
#   objective_function,
#   x0 = scalar0.flatten(),
#   method='SLSQP',  # Suitable for constrained optimization
#   bounds=bounds,
#   constraints=[linear_constraint, last_row_constraint],
#   options={'maxiter': 1000},
#   callback=callback_function,
# )

# from scipy.optimize import newton

# result = newton(
#   objective_function,
#   x0 = scalar0.flatten(),
#   fprime=None,
#   args=(),
#   tol=1e-6,
#   maxiter=1000,
#   fprime2=None,
#   callback=callback_function,
# )

def newton_raphson(f, jac, x0, tol=1e-8, max_iter=50):
  x = x0.copy()
  for i in range(max_iter):
    J = jac(f, x)
    fx = f(x)
    dx = np.linalg.solve(J, -fx)
    x += dx
    if np.linalg.norm(dx) < tol:
      print(f"Converged at step {i}")
      return x
    plot_func(x)
  raise RuntimeError("Did not converge")

def numerical_jacobian(f, x, eps=1e-8):
  """
  计算函数 f 在点 x 处的数值雅可比矩阵。
  输入：
    f: callable, f(x) 返回 shape=(n,)
    x: numpy.ndarray, shape=(n,)
    eps: float, 差分步长
  输出：
    J: numpy.ndarray, shape=(n, n)，Jacobian matrix
  """
  x = x.astype(float)
  n = len(x)
  fx = f(x)
  J = np.zeros((n, n))

  for i in range(n):
    x_eps = x.copy()
    x_eps[i] += eps
    f_eps = f(x_eps)
    J[:, i] = (f_eps - fx) / eps

  return J

sol = newton_raphson(objective_function, numerical_jacobian, scalar0.flatten())

# Reshape the result back to 2D
# x_opt = reshape_x(result.x)

# print(x_opt)



