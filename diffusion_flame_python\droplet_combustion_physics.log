This is pdfTeX, Version 3.141592653-2.6-1.40.24 (TeX Live 2022) (preloaded format=pdflatex 2022.10.21)  7 JUL 2025 18:08
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**droplet_combustion_physics
(./droplet_combustion_physics.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-02-24> (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/article.cls
Document Class: article 2021/10/04 v1.4n Standard LaTeX document class
(e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count185
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@subparagraph=\count190
\c@figure=\count191
\c@table=\count192
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2021/10/15 v2.17l AMS math features
\@mathmargin=\skip49

For additional information on amsmath, use the `?' option.
(e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks16
\ex@=\dimen139
)) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen140
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2021/08/26 v2.02 operator names
)
\inf@bad=\count193
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count194
\leftroot@=\count195
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count196
\DOTSCASE@=\count197
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box50
\strutbox@=\box51
\big@size=\dimen141
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count198
\c@MaxMatrixCols=\count199
\dotsspace@=\muskip16
\c@parentequation=\count266
\dspbrk@lvl=\count267
\tag@help=\toks17
\row@=\count268
\column@=\count269
\maxfields@=\count270
\andhelp@=\toks18
\eqnshift@=\dimen142
\alignsep@=\dimen143
\tagshift@=\dimen144
\tagwidth@=\dimen145
\totwidth@=\dimen146
\lineht@=\dimen147
\@envbody=\toks19
\multlinegap=\skip50
\multlinetaggap=\skip51
\mathdisplay@stack=\toks20
LaTeX Info: Redefining \[ on input line 2938.
LaTeX Info: Redefining \] on input line 2939.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks21
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
))
\Gin@req@height=\dimen148
\Gin@req@width=\dimen149
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count271
\float@exts=\toks22
\float@box=\box52
\@float@everytoks=\toks23
\@floatcapt=\box53
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count272
\Gm@cntv=\count273
\c@Gm@tempcnt=\count274
\Gm@bindingoffset=\dimen150
\Gm@wd@mp=\dimen151
\Gm@odd@mp=\dimen152
\Gm@even@mp=\dimen153
\Gm@layoutwidth=\dimen154
\Gm@layoutheight=\dimen155
\Gm@layouthoffset=\dimen156
\Gm@layoutvoffset=\dimen157
\Gm@dimlist=\toks24
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2022-02-21 v7.00n Hypertext links for LaTeX
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen158
\Hy@linkcounter=\count275
\Hy@pagecounter=\count276
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2022-02-21 v7.00n Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count277
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2022-02-21 v7.00n Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4137.
Package hyperref Info: Link nesting OFF on input line 4142.
Package hyperref Info: Hyper index ON on input line 4145.
Package hyperref Info: Plain pages OFF on input line 4152.
Package hyperref Info: Backreferencing OFF on input line 4157.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4390.
\c@Hy@tempcnt=\count278
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4749.
\XeTeXLinkMargin=\dimen159
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count279
\Field@Width=\dimen160
\Fld@charsize=\dimen161
Package hyperref Info: Hyper figures OFF on input line 6027.
Package hyperref Info: Link nesting OFF on input line 6032.
Package hyperref Info: Hyper index ON on input line 6035.
Package hyperref Info: backreferencing OFF on input line 6042.
Package hyperref Info: Link coloring OFF on input line 6047.
Package hyperref Info: Link coloring with OCG OFF on input line 6052.
Package hyperref Info: PDF/A mode OFF on input line 6057.
LaTeX Info: Redefining \ref on input line 6097.
LaTeX Info: Redefining \pageref on input line 6101.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count280
\c@Item=\count281
\c@Hfootnote=\count282
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2022-02-21 v7.00n Hyperref driver for pdfTeX
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count283
\c@bookmark@seq@number=\count284
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 286.
)
\Hy@SectionHShift=\skip52
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/tools/array.sty
Package: array 2021/10/04 v2.5f Tabular extension package (FMi)
\col@sep=\dimen162
\ar@mcellbox=\box54
\extrarowheight=\dimen163
\NC@list=\toks25
\extratabsurround=\skip53
\backup@length=\skip54
\ar@cellbox=\box55
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen164
\lightrulewidth=\dimen165
\cmidrulewidth=\dimen166
\belowrulesep=\dimen167
\belowbottomsep=\dimen168
\aboverulesep=\dimen169
\abovetopsep=\dimen170
\cmidrulesep=\dimen171
\cmidrulekern=\dimen172
\defaultaddspace=\dimen173
\@cmidla=\count285
\@cmidlb=\count286
\@aboverulesep=\dimen174
\@belowrulesep=\dimen175
\@thisruleclass=\count287
\@lastruleclass=\count288
\@thisrulewidth=\dimen176
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)


Package natbib Warning: The `cite' package should not be used
(natbib)                with natbib. Use option `sort' instead.

\bibhang=\skip55
\bibsep=\skip56
LaTeX Info: Redefining \cite on input line 694.
LaTeX Info: Redefining \citenum on input line 708.
\c@NAT@ctr=\count289
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2022-02-07 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count290
\l__pdf_internal_box=\box56
) (./droplet_combustion_physics.aux)
\openout1 = `droplet_combustion_physics.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count291
\scratchdimen=\dimen177
\scratchbox=\box57
\nofMPsegments=\count292
\nofMParguments=\count293
\everyMPshowfont=\toks26
\MPscratchCnt=\count294
\MPscratchDim=\dimen178
\MPnumerator=\count295
\makeMPintoPDFobject=\count296
\everyMPtoPDFconversion=\toks27
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 21.
(e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count297
)
LaTeX Info: Redefining \ref on input line 21.
LaTeX Info: Redefining \pageref on input line 21.
LaTeX Info: Redefining \nameref on input line 21.
 (./droplet_combustion_physics.out) (./droplet_combustion_physics.out)
\@outlinefile=\write3
\openout3 = `droplet_combustion_physics.out'.

LaTeX Font Info:    Trying to load font information for U+msa on input line 24.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 24.
 (e:/Program Files (x86)/texlive/2022/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) [1

{e:/Program Files (x86)/texlive/2022/texmf-var/fonts/map/pdftex/updmap/pdftex.map}] [2] [3] [4] [5] [6] [7] [8] [9]
LaTeX Font Info:    Font shape `OT1/cmtt/bx/n' in size <12> not available
(Font)              Font shape `OT1/cmtt/m/n' tried instead on input line 466.
 [10] [11] [12] [13] [14]
Overfull \hbox (3.98233pt too wide) in paragraph at lines 729--729
[]        \OT1/cmtt/m/n/12 # Right-hand side B vector (explicit part from previous time step)[] 
 []


Overfull \hbox (22.50734pt too wide) in paragraph at lines 747--747
[]\OT1/cmtt/m/n/12 c0 = ((T_inf + Y_O_inf*Qc/nu/cp)/Bm * np.log(1+Bm) * lambda_bd/lambda_d[-1] -[] 
 []

[15] [16] (./droplet_combustion_physics.bbl) [17] (./droplet_combustion_physics.aux)
Package rerunfilecheck Info: File `droplet_combustion_physics.out' has not changed.
(rerunfilecheck)             Checksum: 41ED792EDEE3F07F45B6785C82326F38;10036.
 ) 
Here is how much of TeX's memory you used:
 10689 strings out of 478268
 166156 string characters out of 5840330
 469297 words of memory out of 5000000
 28573 multiletter control sequences out of 15000+600000
 481352 words of font info for 74 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 60i,12n,63p,640b,469s stack positions out of 10000i,1000n,20000p,200000b,200000s
{e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}<e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi12.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi6.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmr17.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmti12.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/amsfonts/cm/cmtt12.pfb><e:/Program Files (x86)/texlive/2022/texmf-dist/fonts/type1/public/cm-super/sfrm1200.pfb>
Output written on droplet_combustion_physics.pdf (17 pages, 273821 bytes).
PDF statistics:
 546 PDF objects out of 1000 (max. 8388607)
 488 compressed objects within 5 object streams
 188 named destinations out of 1000 (max. 500000)
 425 words of extra memory for PDF output out of 10000 (max. 10000000)

