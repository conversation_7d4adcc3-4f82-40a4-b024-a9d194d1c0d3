
"""Copy with surface reaction"""
import numpy as np
import cantera as ct

# surface evaporation
def Hertz_<PERSON>en(P, M, Psat, Tsat, sticking=1.):
  mdot_vap = sticking * np.sqrt(
    M / (2 * np.pi * ct.gas_constant * Tsat)
  ) * (P - Psat)
  return mdot_vap

# surface temperature
def energy_balance(lam, h_vap):
  - lam * (T_surf - T_cell) / dr = mdot_vap * h_vap
  return T_surf

# surface species
def species_balance(scalar, mdot_vap):
  mdot = np.zeros_like(scalar)
  mdot[0] = mdot_vap
  mdot_vap * scalar - self.rho * self.D * (Y_cell - Y_surf) / dr = mdot

