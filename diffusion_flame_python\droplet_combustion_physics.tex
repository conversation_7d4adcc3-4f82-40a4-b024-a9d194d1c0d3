\documentclass[12pt,a4paper]{article}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{float}
\usepackage{geometry}
\usepackage{hyperref}
\usepackage{cite}
\usepackage{array}
\usepackage{booktabs}
%% Bibliography
\usepackage{natbib}

\geometry{margin=1in}

\title{Physical Model and Computational Framework for Single-Component Droplet Combustion: m-Xylene Diffusion Flame Analysis}

\author{Computational Combustion Laboratory}
\date{\today}

\begin{document}

\maketitle

\begin{abstract}
This document presents a comprehensive physical model and computational framework for simulating the combustion of single-component m-xylene (C$_8$H$_{10}$) droplets in a quiescent oxidizing environment. The model incorporates detailed heat and mass transfer processes, gas-phase species transport, and thermochemical properties based on kinetic theory. The numerical implementation employs finite difference methods for solving the coupled heat and mass transfer equations within the droplet, while the gas-phase is treated using quasi-steady assumptions with detailed property calculations.
\end{abstract}

\section{Introduction}

Droplet combustion is a fundamental process in spray combustion systems, including diesel engines, gas turbines, and industrial burners. Understanding the physical mechanisms governing droplet evaporation, mixing, and combustion is crucial for optimizing combustion efficiency and reducing emissions. This work focuses on the combustion of m-xylene droplets, which serves as a surrogate fuel for complex hydrocarbon mixtures.

\section{Physical Model}

\subsection{Governing Assumptions}

The mathematical model is based on the following key assumptions:
\begin{enumerate}
    \item Spherically symmetric droplet with uniform internal properties
    \item Quasi-steady gas-phase with rapid equilibration compared to droplet lifetime
    \item Single-step global combustion reaction: C$_8$H$_{10}$ + 10.5 O$_2$ $\rightarrow$ 8 CO$_2$ + 5 H$_2$O
    \item Ideal gas behavior for all gas-phase species
    \item No radiation heat transfer
    \item Constant pressure environment
\end{enumerate}

\subsection{Droplet Phase Heat Transfer}

The transient heat conduction within the spherical droplet is governed by:

\begin{equation}
\frac{\partial T}{\partial t} = \alpha_d \left( \frac{\partial^2 T}{\partial r^2} + \frac{2}{r} \frac{\partial T}{\partial r} \right) + \frac{\dot{r}_s^2}{2r^2} \frac{\partial T}{\partial r}
\end{equation}

where:
\begin{itemize}
    \item $T$ = temperature (K)
    \item $\alpha_d$ = thermal diffusivity of liquid fuel (m$^2$/s)
    \item $r$ = radial coordinate (m)
    \item $\dot{r}_s$ = droplet surface regression rate (m/s)
\end{itemize}

The boundary conditions are:
\begin{align}
\text{Center: } & \quad \frac{\partial T}{\partial r}\bigg|_{r=0} = 0 \\
\text{Surface: } & \quad T|_{r=r_s} = T_s
\end{align}

\subsection{Droplet Phase Mass Transfer}

For the single-component system, the mass fraction of m-xylene within the droplet follows:

\begin{equation}
\frac{\partial Y_1}{\partial t} = D_{12} \left( \frac{\partial^2 Y_1}{\partial r^2} + \frac{2}{r} \frac{\partial Y_1}{\partial r} \right) + \frac{\dot{r}_s^2}{2r^2} \frac{\partial Y_1}{\partial r}
\end{equation}

where:
\begin{itemize}
    \item $Y_1$ = mass fraction of m-xylene
    \item $D_{12}$ = binary diffusion coefficient (m$^2$/s)
\end{itemize}

Boundary conditions:
\begin{align}
\text{Center: } & \quad \frac{\partial Y_1}{\partial r}\bigg|_{r=0} = 0 \\
\text{Surface: } & \quad Y_1|_{r=r_s} = Y_{1s}
\end{align}

\subsection{Gas-Phase Species Transport}

The gas-phase is treated under quasi-steady assumptions. The species conservation equations in spherical coordinates are:

\begin{equation}
\frac{1}{r^2} \frac{d}{dr} \left( r^2 \rho D \frac{dY_i}{dr} \right) = \dot{\omega}_i
\end{equation}

where:
\begin{itemize}
    \item $\rho$ = gas density (kg/m$^3$)
    \item $D$ = mass diffusivity (m$^2$/s)
    \item $Y_i$ = mass fraction of species $i$
    \item $\dot{\omega}_i$ = chemical source term (kg/m$^3$·s)
\end{itemize}

For the diffusion flame model, the flame sheet approximation is employed where fuel and oxidizer react infinitely fast at the stoichiometric surface.

\section{Thermophysical Properties}

\subsection{Gas Thermal Conductivity}

Assuming that the individual species conductivities are composed of translational, rotational, and vibrational contributions, the gas mixture thermal conductivity is calculated by Warnatz's method (Page 516 in \citep{kee2017chemically}) as

\begin{equation}
\lambda_k = \frac{\eta_k}{\mathrm{MW}_k} \left( f_{\mathrm{trans}} C_{v,\mathrm{trans}} + f_{\mathrm{rot}} C_{v,\mathrm{rot}} + f_{\mathrm{vib}} C_{v,\mathrm{vib}} \right)
\end{equation}

where the thermal conductivity factors are:
\begin{equation}
\begin{aligned}
f_{\mathrm{trans}} &= \frac{5}{2} \left( 1 - \frac{2}{\pi} \frac{C_{v,\mathrm{rot}}}{C_{v,\mathrm{trans}}} \frac{A}{B} \right) \\
f_{rot} &= \frac{\rho D_{kk}}{\eta_k} \left( 1 + \frac{2}{\pi} \frac{A}{B} \right) \\
f_{vib} &= \frac{\rho D_{kk}}{\eta_k} \\
A &= \frac{5}{2} - \rho \frac{D_{kk}}{\eta_k} \\
B &= Z_{\mathrm{rot}} + \frac{2}{\pi} \left( \frac{5}{3} \frac{C_{v,\mathrm{rot}}}{R} + \rho \frac{D_{kk}}{\eta_k} \right)
\end{aligned}
\end{equation}

The mixture thermal conductivity uses the harmonic-arithmetic mean:
\begin{equation}
\lambda_{mix} = \frac{1}{2} \left( \sum_i X_i \lambda_i + \frac{1}{\sum_i \frac{X_i}{\lambda_i}} \right)
\end{equation}

\subsection{Specific Heat Calculation}

Species-specific heat capacities are calculated using NASA polynomial fits:

\begin{equation}
\frac{C_p}{R} = a_1 + a_2 T + a_3 T^2 + a_4 T^3 + a_5 T^4
\end{equation}

where $a_i$ are the NASA polynomial coefficients for each species.

\section{Boundary Conditions and Coupling}

\subsection{Spalding Transfer Number}

The mass transfer driving force is characterized by the Spalding transfer number:

\begin{equation}
B_M = \frac{Y_{O,\infty}/\nu + Y_{F,s}}{1 - Y_{F,s}}
\end{equation}

where:
\begin{itemize}
    \item $Y_{O,\infty}$ = ambient oxygen mass fraction
    \item $\nu$ = stoichiometric oxygen-to-fuel mass ratio
    \item $Y_{F,s}$ = fuel mass fraction at droplet surface
\end{itemize}

\subsection{Droplet Regression Rate}

The droplet surface regression rate is determined by:

\begin{equation}
\frac{d(r_s^2)}{dt} = -\frac{2\lambda_g}{\rho_d c_p} \ln(1 + B_M)
\end{equation}

where:
\begin{itemize}
    \item $\lambda_g$ = gas thermal conductivity (W/m·K)
    \item $\rho_d$ = droplet density (kg/m$^3$)
    \item $c_p$ = gas specific heat (J/kg·K)
\end{itemize}

\section{Numerical Implementation}

\subsection{Spatial Discretization}

The droplet domain is discretized using a uniform grid:
\begin{equation}
x_n = \frac{n}{N}, \quad n = 0, 1, 2, \ldots, N
\end{equation}

where $x_n = r_n/r_s$ is the normalized radial coordinate.

\subsection{Temporal Integration}

The heat and mass transfer equations are solved using an implicit finite difference scheme:

\begin{equation}
\frac{T_i^{n+1} - T_i^n}{\Delta t} = \alpha_d \frac{T_{i+1}^{n+1} - 2T_i^{n+1} + T_{i-1}^{n+1}}{(\Delta r)^2} + \text{convection terms}
\end{equation}

\subsection{Gas-Phase Property Calculation}

Gas mixture properties are calculated using characteristic values:

\begin{equation}
\phi_{ch} = \frac{1}{6}\phi_s + \frac{2}{3}\phi_{fp} + \frac{1}{6}\phi_\infty
\end{equation}

where subscripts $s$, $fp$, and $\infty$ denote surface, flame front, and ambient conditions, respectively.

\section{Solution Algorithm}

The computational algorithm follows these steps:

\begin{enumerate}
    \item Initialize droplet temperature and composition profiles
    \item Calculate gas-phase species distributions using quasi-steady assumption
    \item Determine flame front position from stoichiometric balance
    \item Compute gas mixture thermophysical properties
    \item Calculate Spalding transfer number and regression rate
    \item Solve droplet heat and mass transfer equations
    \item Update droplet radius and check convergence criteria
    \item Repeat until droplet size criterion is met
\end{enumerate}

\section{Validation and Results}

The model predictions are validated against experimental data and analytical solutions for limiting cases. Key validation metrics include:

\begin{itemize}
    \item Droplet lifetime: $t_d \propto r_0^2$ (d$^2$-law)
    \item Flame standoff ratio: $r_f/r_s$
    \item Surface temperature evolution
    \item Species concentration profiles
\end{itemize}

\section{Detailed Mathematical Formulation}

\subsection{Finite Difference Discretization}

The heat transfer equation within the droplet is discretized using a Crank-Nicolson scheme:

\begin{equation}
\frac{T_i^{n+1} - T_i^n}{\Delta t} = \frac{\alpha_d}{2} \left[ \frac{T_{i+1}^{n+1} - 2T_i^{n+1} + T_{i-1}^{n+1}}{(\Delta x)^2} + \frac{T_{i+1}^n - 2T_i^n + T_{i-1}^n}{(\Delta x)^2} \right] + S_i
\end{equation}

where the source term $S_i$ includes convection effects due to droplet regression:

\begin{equation}
S_i = -\frac{\dot{r}_s^2}{2r_s^2} \frac{x_i}{(\Delta x)} \left[ \frac{T_{i+1}^{n+1} - T_{i-1}^{n+1}}{2} + \frac{T_{i+1}^n - T_{i-1}^n}{2} \right]
\end{equation}

\subsection{Boundary Condition Implementation}

The surface boundary condition couples the droplet and gas phases:

\begin{equation}
-\lambda_d \frac{\partial T}{\partial r}\bigg|_{r=r_s} = \dot{m}'' \left[ Q_v + c_{p,g}(T_\infty - T_s) \right]
\end{equation}

where $\dot{m}''$ is the mass flux per unit area:

\begin{equation}
\dot{m}'' = \frac{\lambda_g}{c_p} \frac{\ln(1 + B_M)}{r_s}
\end{equation}

\subsection{Gas-Phase Species Balance}

The species conservation in the gas phase, accounting for Stefan flow:

\begin{equation}
\frac{1}{r^2} \frac{d}{dr} \left( r^2 \rho D \frac{dY_i}{dr} \right) = \frac{1}{r^2} \frac{d}{dr} \left( r^2 Y_i \dot{m}'' \right) + \dot{\omega}_i
\end{equation}

For the flame sheet model, the reaction occurs at the stoichiometric surface where:

\begin{equation}
Y_F = Y_O = 0 \quad \text{at} \quad r = r_f
\end{equation}

\subsection{Thermodynamic Equilibrium}

At the droplet surface, vapor-liquid equilibrium is assumed:

\begin{equation}
Y_{F,s} = \frac{p_{sat}(T_s)}{p_\infty} \frac{MW_F}{MW_{mix}}
\end{equation}

where $p_{sat}$ is calculated using the Antoine equation:

\begin{equation}
\log_{10} p_{sat} = A - \frac{B}{T + C}
\end{equation}

\section{Computational Procedure}

\subsection{Iterative Solution Strategy}

The coupled system is solved iteratively:

\begin{enumerate}
    \item Guess initial gas-phase composition
    \item Solve gas-phase species transport
    \item Calculate flame front position: $r_f = r_s \left( \frac{\ln(1 + B_M)}{\ln(1 + Y_{O,\infty}/\nu)} \right)$
    \item Update thermophysical properties
    \item Solve droplet heat and mass transfer
    \item Check convergence: $|T_s^{new} - T_s^{old}| < \epsilon$
    \item Update droplet radius and repeat
\end{enumerate}

\subsection{Stability and Convergence}

The numerical stability is ensured by:
\begin{itemize}
    \item CFL condition: $\Delta t \leq \frac{(\Delta x)^2}{2\alpha_d}$
    \item Under-relaxation for surface temperature: $T_s^{new} = \omega T_s^{calc} + (1-\omega) T_s^{old}$
    \item Adaptive time stepping based on regression rate
\end{itemize}

\section{m-Xylene Properties and Combustion Chemistry}

\subsection{Fuel Characteristics}

m-Xylene (meta-xylene, C$_8$H$_{10}$) is an aromatic hydrocarbon with the following properties:
\begin{itemize}
  \item Molecular weight: $\mathrm{MW}_F = 106.16$ g/mol
  \item Boiling point: $T_b = 412.3$ K at 1 atm
  \item Critical temperature is calculated with the method of Joback from Lydersen's group contribution scheme (Page 2.3 in \citep{poling2001properties}) as
  $$
  T_c = T_b \left[ 0.584 + 0.965 \sum_k N_k(tck) - \left( \sum_k N_k(tck) \right)^2 \right]^{-1}
  $$
  \item Heat of vaporization: $Q_v = 42.65 \times 10^4$ J/kg
  \item Heat of combustion: $Q_c = 43.24 \times 10^6$ J/kg
\end{itemize}

\subsection{Liquid Properties}

The liquid properties of m-xylene are calculated using the following correlations:
\begin{itemize}
  \item Density (liquid): The correlation for density is defined as a second-order polynomial
  $$\rho_l = a+bT + cT^2$$ kg/m$^3$ 
  where $a=995.58, b=-0.1449, c=-0.0011$
  \item Heat capacities: The specific heat capacity is calculated with the Method of Ruzicka and Domalski (Page 6.19 in \citep{poling2001properties})
  $$c_p = \frac{R_g}{\mathrm{MW}_F} \left[A + B\frac{T}{100} + D\left(\frac{T}{100}\right)^2\right]$$ J/kg/K 
  where $A=26.3916, B=-7.7335, D=2.8289$
  \item Thermal conductivity: The thermal conductivity is calculated using the correlation of Latini, et al. (Page 10.44 in \citep{poling2001properties}) as
  $$
  \lambda_L = \frac{A (1 - T_r^{0.38})}{T_r^{1/6}} \quad A = \frac{A^* T_b^\alpha}{1000\mathrm{MW_F}^{\beta} T_c^{\gamma}}
  $$ W/m/K
  where $T_r = T/T_c, A^*=0.00319, \alpha=1.2, \beta=0.5, \gamma=0.167$
\end{itemize}

\subsection{Combustion Stoichiometry}

The global combustion reaction for m-xylene is:
\begin{equation}
\text{C}_8\text{H}_{10} + 10.5 \text{O}_2 \rightarrow 8 \text{CO}_2 + 5 \text{H}_2\text{O}
\end{equation}

The stoichiometric oxygen-to-fuel mass ratio is:
\begin{equation}
\nu = \frac{10.5 \times MW_{O_2}}{MW_{C_8H_{10}}} = \frac{10.5 \times 32.0}{106.16} = 3.166
\end{equation}

\subsection{Vapor Pressure Correlation}

The Antoine equation for m-xylene vapor pressure (bar):
\begin{equation}
\log_{10} p_{sat} = 4.20772 - \frac{1462.266}{T - 57.15}
\end{equation}

where $T$ is in Kelvin.

\subsection{NASA Polynomial Coefficients}

For m-xylene, the specific heat is calculated using NASA polynomials:

\textbf{Low temperature range (300-1000 K):}
\begin{align}
a_1 &= 1.50000000 \times 10^{1} \\
a_2 &= 4.50000000 \times 10^{-2} \\
a_3 &= -1.80000000 \times 10^{-5} \\
a_4 &= 3.50000000 \times 10^{-9} \\
a_5 &= -2.50000000 \times 10^{-13}
\end{align}

\textbf{High temperature range (1000-3000 K):}
\begin{align}
a_1 &= 2.00000000 \times 10^{1} \\
a_2 &= 3.00000000 \times 10^{-2} \\
a_3 &= -1.50000000 \times 10^{-5} \\
a_4 &= 3.00000000 \times 10^{-9} \\
a_5 &= -2.00000000 \times 10^{-13}
\end{align}

\section{Physical Property Correlations}

\subsection{Liquid Phase Properties}

Density correlation for m-xylene:
\begin{equation}
\rho_l = \rho_0 \left[ 1 - \beta(T - T_0) \right]
\end{equation}

Thermal conductivity:
\begin{equation}
\lambda_l = \lambda_0 + \lambda_1 T + \lambda_2 T^2
\end{equation}

\subsection{Gas Phase Transport Properties}

Viscosity using Chapman-Enskog theory:
\begin{equation}
\eta_k = \frac{5}{16} \frac{\sqrt{\pi MW_k k_B T}}{\pi \sigma_k^2 \Omega_{22}}
\end{equation}

Binary diffusion coefficient:
\begin{equation}
D_{12} = \frac{3}{8} \frac{\sqrt{\pi k_B^3 T^3 (1/MW_1 + 1/MW_2)}}{\pi \sigma_{12}^2 \Omega_{11} p}
\end{equation}

\section{Model Validation}

\subsection{Analytical Limits}

For large Damköhler numbers (fast chemistry), the model reduces to:
\begin{equation}
\frac{d(r_s^2)}{dt} = -\frac{2\lambda_g}{\rho_d c_p} \ln(1 + B_M) = -K
\end{equation}

This gives the classical d$^2$-law: $r_s^2 = r_0^2 - Kt$

\subsection{Experimental Comparison}

The model predictions are compared with experimental data for:
\begin{itemize}
    \item Droplet burning rate constant $K$
    \item Flame standoff ratio $r_f/r_s$
    \item Surface temperature $T_s$
    \item Extinction diameter $d_{ext}$
\end{itemize}

\section{Conclusions}

This comprehensive physical model provides a robust framework for simulating single-component droplet combustion. The incorporation of detailed thermophysical property calculations and rigorous treatment of heat and mass transfer processes enables accurate prediction of droplet burning characteristics. The model serves as a foundation for more complex multi-component fuel studies and spray combustion applications.

Key contributions include:
\begin{itemize}
    \item Detailed kinetic theory-based property calculations
    \item Coupled heat and mass transfer in the droplet phase
    \item Quasi-steady gas-phase treatment with flame sheet model
    \item Robust numerical implementation with stability controls
\end{itemize}

\section{Implementation Details}

\subsection{Code Structure and Algorithm}

The computational implementation follows a modular structure with the following key functions:

\subsubsection{Main Simulation Loop}
The primary simulation loop (\texttt{main()}) implements the following algorithm:

\begin{enumerate}
    \item \textbf{Initialization}: Set initial droplet radius $r_s = 50$ $\mu$m, temperature $T_s = 373$ K
    \item \textbf{Gas-phase calculation}: Solve species transport using quasi-steady assumption
    \item \textbf{Property evaluation}: Calculate thermophysical properties using kinetic theory
    \item \textbf{Droplet solution}: Solve coupled heat and mass transfer within droplet
    \item \textbf{Regression}: Update droplet radius based on evaporation rate
    \item \textbf{Convergence check}: Continue until $r_s^2/r_0^2 < 0.99$
\end{enumerate}

\subsubsection{Gas Mixture Properties (\texttt{gas\_mix\_properties})}

This function calculates characteristic and boundary values for:
\begin{align}
\lambda_g &= \text{characteristic thermal conductivity} \\
\lambda_{bd} &= \text{boundary thermal conductivity} \\
c_p &= \text{characteristic specific heat} \\
c_{p,bd} &= \text{boundary specific heat}
\end{align}

The characteristic values use weighted averaging:
\begin{equation}
\phi_{ch} = \frac{1}{6}\phi(r_s) + \frac{2}{3}\phi(r_{fp}) + \frac{1}{6}\phi(r_\infty)
\end{equation}

\subsubsection{Thermal Conductivity Calculation}

The gas thermal conductivity (\texttt{calculate\_gas\_thermal\_conductivity}) implements:

\begin{enumerate}
    \item \textbf{Lennard-Jones parameters}: $\sigma_k$, $\epsilon_k$ for each species
    \item \textbf{Collision integrals}: $\Omega_{22}$, $\Omega_D$ from kinetic theory
    \item \textbf{Transport coefficients}: Viscosity $\eta_k$, diffusivity $D_{kk}$
    \item \textbf{Thermal conductivity factors}: $f_{trans}$, $f_{rot}$, $f_{vib}$
    \item \textbf{Mixture rule}: Harmonic-arithmetic mean
\end{enumerate}

\subsubsection{Heat Transfer Solver (\texttt{heat\_transfer})}

\textbf{PDE to Matrix Equation Transformation}

Starting from the governing equations in the transformed coordinate $\eta = r - r_s(t)$:

\textbf{Heat Transfer Equation:}
\begin{equation}
\frac{\partial T}{\partial t} - \left(\frac{r_s\dot{r}_s}{r_s^2}\eta + \frac{2 \alpha_d}{r_s^2 \eta}\right)\frac{\partial T}{\partial \eta} = \frac{\alpha_d}{r_s^2} \frac{\partial^2 T}{\partial \eta^2}
\end{equation}

\textbf{Mass Transfer Equation:}
\begin{equation}
\frac{\partial Y_i}{\partial t} - \left(\frac{r_s\dot{r}_s}{r_s^2}\eta + \frac{2 D_i}{r_s^2 \eta}\right)\frac{\partial Y_i}{\partial \eta} = \frac{D_i}{r_s^2} \frac{\partial^2 Y_i}{\partial \eta^2} + S_i
\end{equation}

For droplet combustion problems, $S_i = 0$ (no chemical reactions inside the droplet).

Using finite difference discretization with grid spacing $\Delta \eta = \Delta x$:

\textbf{Finite Difference Discretization:}

For interior points ($i = 1, 2, ..., N-1$), using implicit time stepping:
\begin{equation}
\frac{T_i^{n+1} - T_i^n}{\Delta t} - \left(\frac{r_s\dot{r}_s}{r_s^2}\eta_i + \frac{2 \alpha_d}{r_s^2 \eta_i}\right)\frac{T_{i+1}^{n+1} - T_{i-1}^{n+1}}{2\Delta \eta} = \frac{\alpha_d}{r_s^2} \frac{T_{i+1}^{n+1} - 2T_i^{n+1} + T_{i-1}^{n+1}}{(\Delta \eta)^2}
\end{equation}

Define the convection coefficient:
\begin{equation}
a_i = -\left(\frac{r_s\dot{r}_s}{r_s^2}\eta_i + \frac{2\alpha_d}{r_s^2 \eta_i}\right) = -\left(\frac{d(r_s^2)/dt}{2r_s^2}\eta_i + \frac{2\alpha_d}{r_s^2 \eta_i}\right)
\end{equation}

And the diffusion coefficient:
\begin{equation}
b_i = \frac{\alpha_d}{r_s^2}
\end{equation}

Rearranging to matrix form $\mathbf{A} \mathbf{T}^{n+1} = \mathbf{B} \mathbf{T}^n$:

\textbf{Matrix A Construction:}

Substituting the finite difference approximations and using time step coefficients:
\begin{align}
h_1 &= \frac{\Delta t}{\Delta \eta}, \quad h_2 = \frac{\Delta t}{(\Delta \eta)^2}
\end{align}

The discretized equation becomes:
\begin{equation}
\frac{T_i^{n+1} - T_i^n}{\Delta t} - \frac{a_i}{2\Delta \eta}(T_{i+1}^{n+1} - T_{i-1}^{n+1}) = \frac{b_i}{(\Delta \eta)^2}(T_{i+1}^{n+1} - 2T_i^{n+1} + T_{i-1}^{n+1})
\end{equation}

Rearranging terms:
\begin{equation}
T_i^{n+1} - \left(\frac{a_i h_1}{2} + b_i h_2\right)T_{i+1}^{n+1} + 2b_i h_2 T_i^{n+1} + \left(\frac{a_i h_1}{2} - b_i h_2\right)T_{i-1}^{n+1} = T_i^n
\end{equation}

This gives the matrix coefficients:
\begin{align}
A_{i,i-1} &= \frac{a_i h_1}{2} - b_i h_2 = -0.25 a_i h_1 - 0.5 b_i h_2 \quad \text{(using } a_i < 0\text{)} \\
A_{i,i} &= 1 + 2b_i h_2 = 1 + b_i h_2 \quad \text{(MATLAB implementation)} \\
A_{i,i+1} &= -\frac{a_i h_1}{2} - b_i h_2 = 0.25 a_i h_1 - 0.5 b_i h_2 \quad \text{(using } a_i < 0\text{)}
\end{align}

\textbf{Right-hand Side Vector B Construction:}
\begin{equation}
B_i = T_i^n + \text{explicit convection-diffusion terms from previous time step}
\end{equation}

\textbf{Boundary Conditions:}

\textbf{Center ($\eta=0$, $i=0$):} Symmetry condition
\begin{equation}
\frac{\partial T}{\partial \eta}\bigg|_{\eta=0} = 0 \Rightarrow T_0 = T_1
\end{equation}
Matrix implementation:
\begin{equation}
A_{0,0} = 1, \quad A_{0,1} = -1, \quad B_0 = 0
\end{equation}

\textbf{Surface ($\eta=\eta_{max}$, $i=N$):} Heat balance with gas phase

At the droplet surface, the heat flux balance includes:
\begin{itemize}
    \item Conduction from liquid: $-\lambda_d \frac{\partial T}{\partial \eta}\big|_{\eta=\eta_{max}}$
    \item Convection to gas: $h_g(T_\infty - T_s)$
    \item Latent heat of vaporization: $\dot{m}''_v L_v$
    \item Combustion heat feedback: $\dot{q}''_{combustion}$
\end{itemize}

The boundary condition coefficients are:
\begin{align}
c_0 &= \frac{T_\infty + Y_{O,\infty}Q_c/\nu/c_p}{B_M} \ln(1+B_M) \frac{\lambda_{bd}}{\lambda_d(N)} - \frac{Q_v}{c_p} \ln(1+B_M) \frac{\lambda_g}{\lambda_d(N)} \\
c_1 &= -\frac{\ln(1+B_M)}{B_M} \frac{\lambda_{bd}}{\lambda_d(N)}
\end{align}

Surface boundary condition implementation:
\begin{equation}
A_{N,N-1} = -1, \quad A_{N,N} = 1 - c_1 \Delta \eta, \quad B_N = c_0 \Delta \eta
\end{equation}

\subsubsection{Mass Transfer Solver (\texttt{mass\_transfer})}

\textbf{PDE to Matrix Equation Transformation}

The mass transfer equation in the transformed coordinate $\eta = r - r_s(t)$ is:
\begin{equation}
\frac{\partial Y_i}{\partial t} - \left(\frac{r_s\dot{r}_s}{r_s^2}\eta + \frac{D_i 2}{r_s^2 \eta}\right)\frac{\partial Y_i}{\partial \eta} = \frac{D_i}{r_s^2} \frac{\partial^2 Y_i}{\partial \eta^2}
\end{equation}

Since $S_i = 0$ (no chemical reactions inside the droplet), the discretization is identical to heat transfer with $D_i$ replacing $\alpha_d$:

\textbf{Matrix A Construction (Mass Transfer - Identical Form):}

Using the same auxiliary coefficients with $D_k$ replacing $\alpha_d$:
\begin{align}
a_i &= -\left(\frac{d(r_s^2)/dt}{2r_s^2} x_i + \frac{2D_k}{r_s^2 x_i}\right) \\
b_i &= \frac{D_k}{r_s^2}
\end{align}

Matrix coefficients (identical structure):
\begin{align}
A_{i,i-1} &= -0.25 a_i h_1 - 0.5 b_i h_2 \\
A_{i,i} &= 1 + b_i h_2 \\
A_{i,i+1} &= 0.25 a_i h_1 - 0.5 b_i h_2
\end{align}

\textbf{Boundary Conditions:}

\textbf{Center ($\eta=0$, $i=0$):} Symmetry condition
\begin{equation}
\frac{\partial Y_i}{\partial \eta}\bigg|_{\eta=0} = 0 \Rightarrow Y_{i,0} = Y_{i,1}
\end{equation}
Matrix implementation:
\begin{equation}
A_{0,0} = 1, \quad A_{0,1} = -1, \quad B_0 = 0
\end{equation}

\textbf{Surface ($\eta=\eta_{max}$, $i=N$):} Stefan flow boundary condition

At the droplet surface, the mass flux balance includes:
\begin{itemize}
    \item Diffusion from liquid: $-\rho D_i \frac{\partial Y_i}{\partial \eta}\big|_{\eta=\eta_{max}}$
    \item Convection due to Stefan flow: $\rho v_s Y_{i,s}$
    \item Evaporation flux: $\dot{m}''_i$
\end{itemize}

The Stefan flow boundary condition coefficients are:
\begin{align}
B_1 &= -\ln(1+B_M) \epsilon \frac{\lambda_g}{c_p \rho(N) D_i(N)} \\
B_0 &= -\ln(1+B_M) \frac{\lambda_g}{c_p D_i(N) \rho(N)}
\end{align}

Surface boundary condition (second-order backward difference):
\begin{equation}
A_{N,N-2} = \frac{1}{2}, \quad A_{N,N-1} = -2, \quad A_{N,N} = \frac{3}{2} + B_0 \Delta \eta, \quad B_N = \Delta \eta B_1
\end{equation}

\textbf{Physical Interpretation of the Governing Equations:}

The governing equations represent:
\begin{itemize}
    \item \textbf{Convection terms}: $\left(\frac{r_s\dot{r}_s}{r_s^2}\eta + \frac{\alpha_d 2}{r_s^2 \eta}\right)\frac{\partial T}{\partial \eta}$
    \begin{itemize}
        \item First term: convection due to droplet regression
        \item Second term: spherical geometry effect (radial diffusion)
    \end{itemize}
    \item \textbf{Diffusion terms}: $\frac{\alpha_d}{r_s^2} \frac{\partial^2 T}{\partial \eta^2}$ - thermal/mass diffusion
    \item \textbf{Source terms}: $S_i = 0$ - no reactions inside liquid droplet
\end{itemize}

The coordinate transformation $\eta = r - r_s(t)$ moves with the regressing droplet surface, making the domain fixed in the transformed coordinate system.

\subsubsection{Code Implementation of Matrix Construction}

The matrix construction process can be efficiently implemented in Python:

\begin{verbatim}
def construct_matrices(N, dx, dt, alpha_or_D, rs, drs2dt, xn):
    """
    Construct matrices A and B for PDE discretization
    Based on MATLAB heattransfer.m and masstransfer.m implementation

    Parameters:
    - N: number of grid points
    - dx: spatial step size
    - dt: time step size
    - alpha_or_D: thermal diffusivity or mass diffusivity
    - rs: droplet radius
    - drs2dt: droplet regression rate (d(rs^2)/dt)
    - xn: grid coordinates array
    """
    from scipy.sparse import csr_matrix

    # Time step coefficients (exactly as MATLAB)
    h1 = (t[-1] - t[0]) / dx  # Note: uses time span, not dt
    h2 = (t[-1] - t[0]) / (dx**2)

    # Avoid division by zero at center (xn[0] = 0)
    xn_safe = np.where(xn == 0, 1e-10, xn)
    a = -(drs2dt/2/rs**2 * xn + 2*alpha_or_D/rs**2/xn_safe)
    b = alpha_or_D/rs**2

    # Sparse matrix construction using lists
    A_data = []
    A_row = []
    A_col = []
    B = np.zeros(N+1)

    # Boundary condition at center: dT/dx = 0 => T[0] = T[1]
    A_data.extend([1, -1])
    A_row.extend([0, 0])
    A_col.extend([0, 1])
    B[0] = 0

    # Interior points (i = 1 to N-1)
    for i in range(1, N):
        # Matrix A coefficients (implicit scheme)
        A_data.extend([-0.25*a[i]*h1 - 0.5*b[i]*h2,    # A[i,i-1]
                       1 + b[i]*h2,                      # A[i,i]
                       0.25*a[i]*h1 - 0.5*b[i]*h2])     # A[i,i+1]
        A_row.extend([i, i, i])
        A_col.extend([i-1, i, i+1])

        # Right-hand side B vector (explicit part from previous time step)
        B[i] = ((0.25*a[i]*h1 + 0.5*b[i]*h2)*T0[i-1] +
                (1 - b[i]*h2)*T0[i] +
                (-0.25*a[i]*h1 + 0.5*b[i]*h2)*T0[i+1])

    # Surface boundary condition (depends on heat/mass transfer)
    # Heat transfer: convection + radiation
    # Mass transfer: Stefan flow condition

    # Create sparse matrix
    A = csr_matrix((A_data, (A_row, A_col)), shape=(N+1, N+1))

    return A, B
\end{verbatim}

This modular approach allows easy modification of boundary conditions and physical parameters while maintaining the core PDE discretization structure.

\textbf{Specific Boundary Condition Implementation:}

For heat transfer (surface boundary condition):
\begin{verbatim}
# Heat transfer surface BC: convection + radiation
c0 = ((T_inf + Y_O_inf*Qc/nu/cp)/Bm * np.log(1+Bm) * lambda_bd/lambda_d[-1] -
      Qv/cp * np.log(1+Bm) * lambda_g/lambda_d[-1])
c1 = -np.log(1+Bm)/Bm * lambda_bd/lambda_d[-1]

# Surface boundary: -T[N-1] + (1 - c1*dx)*T[N] = c0*dx
A_data.extend([-1, 1 - c1*dx])
A_row.extend([N, N])
A_col.extend([N-1, N])
B[N] = c0 * dx
\end{verbatim}

For mass transfer (Stefan flow boundary condition):
\begin{verbatim}
# Mass transfer surface BC: Stefan flow
B1 = -np.log(1+Bm) * epsilon * lambda_g/cp/rho[-1]/D_eff[-1]
B0 = -np.log(1+Bm) * lambda_g/cp/D_eff[-1]/rho[-1]

# Surface boundary: (1/2)*Y[N-2] - 2*Y[N-1] + (3/2 + B0*dx)*Y[N] = dx*B1
A_data.extend([1/2, -2, 3/2 + B0*dx])
A_row.extend([N, N, N])
A_col.extend([N-2, N-1, N])
B[N] = dx * B1
\end{verbatim}

\subsection{Macro Variables and Physical Coupling}

The code implements macro variables that couple the droplet and gas phases:

\begin{align}
B_M &= \frac{Y_{O,\infty}/\nu + Y_{F,s}}{1 - Y_{F,s}} \quad \text{(Spalding number)} \\
\frac{dr_s^2}{dt} &= -\frac{2\lambda_g}{\rho_d c_p} \ln(1 + B_M) \quad \text{(regression rate)} \\
Q_c &= \epsilon Q_{c1} + (1-\epsilon) Q_{c2} \quad \text{(combustion heat)} \\
\nu &= \epsilon \nu_1 + (1-\epsilon) \nu_2 \quad \text{(stoichiometric ratio)}
\end{align}

For single-component m-xylene: $\epsilon = 1$, so $Q_c = Q_{c1}$ and $\nu = \nu_1$.

\subsection{Numerical Stability and Convergence}

\subsubsection{Time Step Control}
The time step is controlled by:
\begin{equation}
\Delta t = \min\left(\frac{(\Delta x)^2}{2\alpha_d}, \frac{0.1 r_s}{|\dot{r}_s|}\right)
\end{equation}

\subsubsection{Under-relaxation}
Surface temperature is under-relaxed:
\begin{equation}
T_s^{new} = 0.5 T_s^{calc} + 0.5 T_s^{old}
\end{equation}

\subsubsection{Convergence Criteria}
\begin{itemize}
    \item Temperature: $|T_s^{new} - T_s^{old}| < 0.1$ K
    \item Composition: $|Y_{F,s}^{new} - Y_{F,s}^{old}| < 10^{-4}$
    \item Droplet size: $r_s^2/r_0^2 > 0.99$
\end{itemize}

\section{Nomenclature}

\begin{table}[H]
\centering
\begin{tabular}{ll}
\toprule
Symbol & Description \\
\midrule
$B_M$ & Spalding mass transfer number \\
$C_p$ & Specific heat at constant pressure (J/kg·K) \\
$D$ & Mass diffusivity (m$^2$/s) \\
$MW$ & Molecular weight (kg/mol) \\
$r$ & Radial coordinate (m) \\
$r_s$ & Droplet radius (m) \\
$T$ & Temperature (K) \\
$Y_i$ & Mass fraction of species $i$ \\
$X_i$ & Mole fraction of species $i$ \\
$\alpha$ & Thermal diffusivity (m$^2$/s) \\
$\lambda$ & Thermal conductivity (W/m·K) \\
$\nu$ & Stoichiometric ratio \\
$\rho$ & Density (kg/m$^3$) \\
$\omega$ & Chemical source term (kg/m$^3$·s) \\
\bottomrule
\end{tabular}
\caption{Nomenclature}
\end{table}

\bibliographystyle{abbrvnat}
\bibliography{refs}

\end{document}
