This is BibTeX, Version 0.99d (TeX Live 2022)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: aluminum.aux
The style file: abbrvnat.bst
Database file #1: mybib.bib
You've used 16 entries,
            2773 wiz_defined-function locations,
            696 strings with 7608 characters,
and the built_in function-call counts, 8781 in all, are:
= -- 748
> -- 646
< -- 5
+ -- 217
- -- 201
* -- 822
:= -- 1391
add.period$ -- 48
call.type$ -- 16
change.case$ -- 112
chr.to.int$ -- 16
cite$ -- 32
duplicate$ -- 328
empty$ -- 626
format.name$ -- 221
if$ -- 1776
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 16
newline$ -- 88
num.names$ -- 64
pop$ -- 201
preamble$ -- 1
purify$ -- 98
quote$ -- 0
skip$ -- 263
stack$ -- 0
substring$ -- 368
swap$ -- 17
text.length$ -- 1
text.prefix$ -- 0
top$ -- 0
type$ -- 171
warning$ -- 0
while$ -- 71
width$ -- 0
write$ -- 215
