# Copyright 2003 <PERSON> et al., LCSR-CNRS
# Copyright 2016 <PERSON> et al., Paris-Saclay University
# Copyright 2024 <PERSON>, <PERSON><PERSON>, Beihang University. 
# E-mail: <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License. 
#
# Reduced gas phase reaction mechanism from: 
# (1) Kinetic Model for Aluminum-Sensitized Ram Accelerator Combustion, 
#     <PERSON> et al., Journal of Propulsion and Power, 2003
# (2) On the role of heterogeneous reactions in aluminum combustion, 
#     <PERSON> et al., Combustion and Flame, 2016
# (3) The Simulation of Different Combustion Stages of Micron-Sized Aluminum Particles,
#     <PERSON><PERSON><PERSON> et al., Applied Science, 2021


cantera-version: 3.0.0
date: Thu, 20 Feb 2025 23:09:32 +0800

units: {length: cm, time: s, quantity: mol, activation-energy: J/mol}

phases:
- name: bulk1
  thermo: fixed-stoichiometry
  elements: [Al]
  species: [AL(B)]
- name: bulk2
  thermo: fixed-stoichiometry
  elements: [Al, O]
  species: [AL2O3(B)]
- name: Al_surface
  thermo: coverage-dependent-surface
  elements: [Al, O]
  species: [AL(L), O(S), ALO(S), ALO2(S), AL2O(S), AL2O2(S), AL2O3(L)]
  kinetics: surface
  reactions: all
  state: {T: 2500.0, P: 1 atm, coverages: {AL(L): 1.0}}
  site-density: 4.42e-9 mol/cm^2
  adjacent-phases: [gas, bulk1, bulk2]
  bulk-phase: [bulk1, bulk2]
- name: gas
  thermo: ideal-gas
  elements: [Al, O, N, Ar]
  species:
    - Al_gas.yaml/species: [AL, ALO, ALO2, AL2O, AL2O2, AL2O3, AL2O3c, AL2O3(l), O, O2, AR, N2]
  kinetics: gas
  reactions: 
    - Al_gas.yaml/reactions: declared-species
  transport: mixture-averaged
  state: {T: 2500.0, P: 1 atm}

species:
- name: AL(L)
  composition: {Al: 1}
  sites: 1
  thermo:
    model: NASA7
    temperature-ranges: [933.61, 5000.0]
    data:
    - [3.81862551, 0.0, 0.0, 0.0, 0.0, -94.9651808, -17.5229704]
    note: J03/67
- name: O(S)
  composition: {O: 1}
  sites: 1
  thermo:
    model: NASA7
    temperature-ranges: [298.15, 1000.0, 5000.0]
    data:
    - [2.93580437, -1.57039962e-03, 2.28766551e-06, -1.51409618e-09, 3.77790328e-13,
      -6.67459696e+04, 3.00986433]
    - [2.5534723, -4.53453938e-05, 6.49525481e-09, 2.40993201e-12, -2.67686341e-16,
      -6.66692894e+04, 4.85546407]
- name: ALO(S)
  sites: 1
  composition: {Al: 1, O: 1}
  thermo:
    model: NASA7
    temperature-ranges: [298.15, 1000.0, 5000.0]
    data:
    - [1.030225, 0.016051481, -3.13113292e-05, 2.70432895e-08, -8.34051961e-12,
      -1.18645191e+04, 16.7621416]
    - [3.13304008, 1.38690021e-03, 6.5522816e-08, -1.2793232e-10, 1.56159681e-14,
      -1.19724161e+04, 8.18060445]
- name: ALO2(S)
  composition: {Al: 1, O: 2}
  sites: 1
  thermo:
    model: NASA7
    temperature-ranges: [298.15, 1000.0, 5000.0]
    data:
    - [3.28073609, 0.0121627259, -1.43245345e-05, 7.53738041e-09, -1.42786616e-12,
      -1.01045848e+05, 7.79737492]
    - [6.44281286, 1.25699978e-03, -5.79816622e-07, 1.16816104e-10, -8.37031429e-15,
      -1.01765392e+05, -7.89346288]
- name: AL2O(S)
  composition: {Al: 2, O: 1}
  sites: 3
  thermo:
    model: NASA7
    temperature-ranges: [298.15, 1000.0, 5000.0]
    data:
    - [3.48108363, 9.28089164e-03, -9.64154252e-06, 4.11911221e-09, -4.93865488e-13,
      -3.21959217e+04, 8.97532922]
    - [6.04014638, 1.11717053e-03, -5.04755647e-07, 1.00450835e-10, -7.33261066e-15,
      -3.28113608e+04, -3.88880172]
- name: AL2O2(S)
  composition: {Al: 2, O: 2}
  sites: 3
  thermo:
    model: NASA7
    temperature-ranges: [298.15, 1000.0, 5000.0]
    data:
    - [2.05534593, 0.01845795, -1.52928896e-05, 3.33958502e-09, 7.73119385e-13,
      -8.50927604e+04, 15.4881191]
    - [7.51391121, 2.87404647e-03, -1.29257074e-06, 2.56396733e-10, -1.86729462e-14,
      -8.64969914e+04, -12.4088924]
- name: AL2O3(L)
  composition: {Al: 2, O: 3}
  sites: 3
  thermo:
    model: NASA7
    temperature-ranges: [300.0, 1000.0, 2327.0]
    data:
    - [-4.9138309, 0.079398443, -1.3237918e-04, 1.044675e-07, -3.156633e-11,
      -2.0262622e+05, 15.478073]
    - [11.833666, 3.7708878e-03, -1.7863191e-07, -5.6008807e-10, 1.4076825e-13,
      -2.0571131e+05, -63.599835]
    note: J12/79
- name: AL(B)
  composition: {Al: 1}
  thermo:
    model: NASA7
    temperature-ranges: [933.61, 6000.0]
    data:
    - [3.81862551, 0.0, 0.0, 0.0, 0.0, -94.9651808, -17.5229704]
    note: J03/67
- name: AL2O3(B)
  composition: {Al: 2, O: 3}
  thermo:
    model: NASA7
    temperature-ranges: [300.0, 1000.0, 2327.0]
    data:
    - [-4.9138309, 0.079398443, -1.3237918e-04, 1.044675e-07, -3.156633e-11,
      -2.0262622e+05, 15.478073]
    - [11.833666, 3.7708878e-03, -1.7863191e-07, -5.6008807e-10, 1.4076825e-13,
      -2.0571131e+05, -63.599835]
    note: J12/79

reactions:
- equation: AL + AL(L) <=> AL(B) + AL(L)  # Reaction 1
# - equation: AL <=> AL(B)  # Reaction 1
  sticking-coefficient: {A: 1.00e+00, b: 0, Ea: 0}
  Motz-Wise: true
- equation: O + AL(L) <=> O(S) + AL(B)  # Reaction 2
  sticking-coefficient: {A: 1.00e+00, b: 0, Ea: 0}
  Motz-Wise: true
- equation: O2 + 2 AL(L) <=> 2 O(S) + 2 AL(B)  # Reaction 3
  sticking-coefficient: {A: 5.00e-03, b: 0, Ea: 0}
- equation: AL(L) + ALO <=> ALO(S) + AL(B)  # Reaction 4
  sticking-coefficient: {A: 1.00e+00, b: 0, Ea: 0}
- equation: ALO(S) + AL(L) <=> AL(L) + O(S) + AL(B)  # Reaction 5
# - equation: ALO(S) <=> O(S) + AL(B)  # Reaction 5
  rate-constant: {A: 2.26e+19, b: 0, Ea: 11840.0}
- equation: AL(L) + O <=> ALO(S)  # Reaction 6
  rate-constant: {A: 2.29e+13, b: 0, Ea: 0}
- equation: AL2O + 3 AL(L) <=> AL2O(S) + 3 AL(B)  # Reaction 7
  sticking-coefficient: {A: 1.00e+00, b: 0, Ea: 0}
- equation: ALO(S) + AL + 2 AL(L) <=> AL2O(S) + 2 AL(B)  # Reaction 8
  rate-constant: {A: 2.29e+13, b: 0, Ea: 0}
- equation: ALO + AL(L) + 2 AL(L) <=> AL2O(S) + 2 AL(B)  # Reaction 9
  rate-constant: {A: 2.29e+13, b: 0, Ea: 0}
- equation: AL(L) + ALO(S) + AL(L) <=> AL2O(S) + AL(B)  # Reaction 10
  rate-constant: {A: 2.26e+19, b: 0, Ea: 0}
- equation: 2 AL(L) + O + AL(L) <=> AL2O(S) + AL(B)  # Reaction 11
  rate-constant: {A: 2.29e+13, b: 0, Ea: 0}
- equation: ALO2 + AL(L) <=> ALO2(S) + AL(B)  # Reaction 12
  sticking-coefficient: {A: 0.75e+00, b: 0, Ea: 0}
- equation: 2 O(S) + AL + AL(B) <=> ALO2(S) + AL(L)  # Reaction 13
  rate-constant: {A: 2.29e+13, b: 0, Ea: 0}
- equation: ALO + O(S) <=> ALO2(S)  # Reaction 14
  rate-constant: {A: 2.29e+13, b: 0, Ea: 0}
- equation: AL2O2 + 3 AL(L) <=> AL2O2(S) + 3 AL(B)  # Reaction 15
  sticking-coefficient: {A: 0.75E+00, b: 0, Ea: 0}
- equation: ALO2 + AL(L) + 2 AL(L) <=> AL2O2(S) + 2 AL(B)  # Reaction 16
  rate-constant: {A: 2.29E+13, b: 0, Ea: 0}
- equation: ALO(S) + ALO + 2 AL(L) <=> AL2O2(S) + 2 AL(B)  # Reaction 17
  rate-constant: {A: 2.29E+13, b: 0, Ea: 0}
- equation: AL2O + O(S) + 2 AL(L) <=> AL2O2(S) + 2 AL(B)  # Reaction 18
  rate-constant: {A: 2.29E+13, b: 0, Ea: 47094}
- equation: AL2O(S) + O <=> AL2O2(S)  # Reaction 19
  rate-constant: {A: 2.29E+13, b: 0, Ea: 0}
- equation: AL2O3 => AL2O3(B)  # Reaction 20
  sticking-coefficient: {A: 1.00E+00, b: 0, Ea: 0}
- equation: ALO2 + ALO(S) + AL(B) => AL2O3(B) + AL(L)  # Reaction 21, treat Y(AL2O3(B)) = 1
  rate-constant: {A: 2.29E+13, b: 0, Ea: 0}
- equation: ALO2(S) + ALO + AL(B) => AL2O3(B) + AL(L)  # Reaction 22
  rate-constant: {A: 2.29E+13, b: 0, Ea: 0}
- equation: AL2O2(S) + O + 3 AL(B) => AL2O3(B) + 3 AL(L)  # Reaction 23
  rate-constant: {A: 2.29E+13, b: 0, Ea: 0}
- equation: 2 ALO(S) + O(S) + 3 AL(B) => AL2O3(B) + 3 AL(L)  # Reaction 24
  rate-constant: {A: 2.26E+19, b: 0, Ea: 0}
- equation: 3 O(S) + 2 AL(L) + 5 AL(B) => AL2O3(B) + 5 AL(L)  # Reaction 25
  rate-constant: {A: 1.00E+48, b: 0, Ea: 0}
# - equation: AL2O2(S) + O(S) + 4 AL(B) => AL2O3(B) + 4 AL(L)
#   rate-constant: {A: 2.26E+19, b: 0, Ea: 0}
# - equation: ALO2(S) + ALO(S) + 2 AL(B) => AL2O3(B) + 2 AL(L)
#   rate-constant: {A: 2.26E+19, b: 0, Ea: 0}
# - equation: AL2O(S) + 2 O(S) + 5 AL(B) => AL2O3(B) + 5 AL(L)
#   rate-constant: {A: 2.26E+19, b: 0, Ea: 0}
