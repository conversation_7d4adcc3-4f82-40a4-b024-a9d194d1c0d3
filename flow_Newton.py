"""1D steady flow model for aluminum droplet combustion."""

import numpy as np
from einops import rearrange
import cantera as ct
from scipy.integrate import solve_ivp

import matplotlib.pyplot as plt
from matplotlib import rcParams
config = {
	"font.family":'sans serif',
	"font.size": 12,
	"mathtext.fontset":'custom',
	"font.sans-serif": ['Arial'],
	"axes.unicode_minus":False,
}
rcParams.update(config)
labelsize = 12


# computational domain
dr = 2e-6 # um
r_min = 105e-6 # um
r_max = r_min * 15
n_r = int((r_max - r_min) / dr)
r = np.linspace(r_min, r_max, n_r)
dr = r[1] - r[0]
R = np.meshgrid(r)[0]         # face of cells, shape (n_r,)
cell_R = (R[1:] + R[:-1]) / 2 # center of cells, shape (n_r-1,)


# initial scalar field
far_T = 2500 # far field temperature
surf_T = 2500 # surface temperature
gas = ct.Solution("./mechs/Al_gas.yaml")
gas.TPX = far_T, ct.one_atm, {'O2':0.21, 'AR':0.79}
scalar_gas = np.zeros((gas.n_species + 1, cell_R.shape[0])) # T,Y at cells
scalar_gas[0] = far_T # initial gas temperature
scalar_gas[0, 0] = surf_T
scalar_gas[1] = 0.5**((np.arange(cell_R.shape[0])+1) ** (1/1.01))
# scalar_gas[1] = 0.5**((np.arange(cell_R.shape[0])+1) ** (1/3.0)) # 1.8))
# scalar_gas[-5] = (np.arange(cell_R.shape[0])/100 + 0.2) * np.exp(-np.arange(cell_R.shape[0])/100) * 1.5
scalar_gas[-3] = (1 - scalar_gas[1] - scalar_gas[-5]) * gas.Y[-3] # initial Y of oxygen
scalar_gas[-2] = (1 - scalar_gas[1] - scalar_gas[-5]) * gas.Y[-2] # initial Y of argon
scalar_gas[1:] /= scalar_gas[1:].sum(axis=0) 
print(R.shape, scalar_gas.shape, scalar_gas[-3][0])


def steady_model(
  t, 
  scalar,
  pressure=ct.one_atm
):
  """1D steady state model.
  Args:
    scalar: numpy.ndarray, (nspecies+1, n_x)
  """
  # scalars in cells
  scalar = rearrange(scalar, '(s d) -> s d', d=cell_R.shape[0])
  scalar = scalar.reshape(-1, cell_R.shape[0])
  scalar[0] = np.clip(scalar[0], a_min=300, a_max=5000)
  scalar[1:] = np.clip(scalar[1:], a_min=0, a_max=None)
  scalar[1:] /= scalar[1:].sum(axis=0)

  # thermochemical properties in cells
  gas_array = ct.SolutionArray(gas, shape=cell_R.shape[0])
  gas_array.TPY = scalar[0], pressure, scalar[1:].T
  Xk = gas_array.X.T # kmol/kmol
  Dk = gas_array.mix_diff_coeffs.T # m2/s
  rho = gas_array.density # kg/m3
  cp = gas_array.cp # J/kg/K
  lam = gas_array.thermal_conductivity # W/m/K = kg*m2/s2/s/m/K
  hk = (gas_array.partial_molar_enthalpies / gas_array.molecular_weights).T # J/kg = m2/s2
  qdot = gas_array.heat_release_rate # W/m3 = J/s/m3 = kg/m/s3
  mdotk = (gas_array.net_production_rates * gas_array.molecular_weights).T # kg/m3/s
  grad_Xk = np.zeros_like(Xk) # kmol/kmol/m
  grad_Xk[:, :-1] = (Xk[:, 1:] - Xk[:, :-1]) / dr

  # cell quantities in FVM
  cell_h = np.sum(scalar[1:] * hk, axis=0)        # shape (n_r-1,), J/kg
  cell_Jk = - rho * scalar[1:] * Dk * grad_Xk
  cell_Jk -= scalar[1:] * np.sum(cell_Jk, axis=0) # shape (n_species, n_r-1), kg/m2/s
  V = (4 * np.pi * cell_R**2) * dr                # shape (n_r-1,), m3
  source_species = mdotk * V                      # shape (n_species, n_r-1), kg/s
  # source_heat = qdot * V # J/s
  
  # face quantities in FVM
  face_hk = (hk[:, 1:] + hk[:, :-1]) / 2
  face_lam = (lam[1:] + lam[:-1]) / 2
  face_grad_T = (scalar[0, 1:] - scalar[0, :-1]) / dr
  # face fluxes
  face_A = 4 * np.pi * R**2
  face_Yk = (scalar[1:, 1:] + scalar[1:, :-1]) / 2 # kg/kg
  face_Jk = (cell_Jk[:, 1:] + cell_Jk[:, :-1]) / 2 # kg/m2/s
  face_Jk -= face_Yk * np.sum(face_Jk, axis=0)
  face_h = (cell_h[:-1] + cell_h[1:]) / 2
  face_q = np.sum(face_hk * face_Jk, axis=0) - face_lam * face_grad_T # J/m2/s

  n_faces = face_A.shape[0]
  face_M = np.zeros(n_faces)
  omega = 0.853  
  face_M[0] = omega * face_A[0]
  for i in range(1, n_faces):
    # total mass source in cell i-1
    face_M[i] = face_M[i-1] - np.sum(mdotk[:, i-1]) * V[i-1]  # kg/s

  # surface bc                                                               # kg/m2/s, surface mass flux from experiment
  Yk_s = scalar[1:, 0]  # 表面处质量分数
  surf_Jk = - omega * Yk_s
  i_Al = gas.species_index('AL')
  surf_Jk[i_Al] += omega  # 实现 delta_Al - Yk_s
  surf_h = hk[:, 0] @ (surf_Jk / omega)
  H_vap = -10.9e6  # 铝蒸发潜热 J/kg
  surf_q = omega * H_vap  # 蒸发引起的热流 (单位：W/m2 = J/s/m2)

  # far field bc
  far_grad_T = 0.0 # (far_g.T - scalar[0, -1]) / (.5 * dr)
  far_grad_X = 0.0
  farT = scalar[0, -1] + far_grad_T * (.5 * dr)
  farX = Xk[:, -1] + far_grad_X * (.5 * dr)
  # farT = far_T
  # farX = scalar0[1:, 0]
  far_g = ct.Solution("./mechs/Al_gas.yaml")
  far_g.TPX = farT, pressure, farX
  far_Dk = far_g.mix_diff_coeffs
  far_hk = far_g.partial_molar_enthalpies / far_g.molecular_weights
  far_rho = far_g.density
  far_lam = far_g.thermal_conductivity
  far_grad_X = (far_g.X - Xk[:, -1]) / (.5 * dr)
  far_grad_T = (far_g.T - scalar[0, -1]) / (.5 * dr)

  far_Jk = - far_rho * far_g.Y * far_Dk * far_grad_X
  far_Jk -= far_g.Y * np.sum(far_Jk, axis=0)
  far_h = far_g.h
  far_q = np.sum(far_hk * far_Jk, axis=0) - far_lam * far_grad_T

  # concatenate boundary fluxes
  face_Yk = np.insert(
      np.insert(face_Yk, 0, Yk_s, axis=-1), -1, far_g.Y, axis=-1)               # shape (n_species, n_r), kg/kg
  face_Jk = np.insert(
      np.insert(face_Jk, 0, surf_Jk, axis=-1), -1, far_Jk, axis=-1)             # shape (n_species, n_r), kg/m2/s
  face_h = np.insert(np.insert(face_h, 0, surf_h), -1, far_h)                   # shape (n_r), J/kg
  face_q = np.insert(np.insert(face_q, 0, surf_q), -1, far_q)                   # shape (n_r), J/m2/s
  # face_M = (4 * np.pi * R[0]**2) * omega * np.ones_like(face_A)

  # compute vector fields
  pm_pt = -(face_M[1:] - face_M[:-1])                                           # shape (n_r-1), kg/s
  pT_pt = 1 / rho / V / cp * (
      -(face_M[1:] * face_h[1:] - face_M[:-1] * face_h[:-1]) - (
        face_q[1:] * face_A[1:] - face_q[:-1] * face_A[:-1]) - (
        pm_pt * cell_h))                                                        # shape (n_species, n_r-1), T/s
  pYk_pt = 1 / rho / V * (
      -(face_M[1:] * face_Yk[:, 1:] - face_M[:-1] * face_Yk[:, :-1]) - (
        face_Jk[:, 1:] * face_A[1:] - face_Jk[:, :-1] * face_A[:-1]) + (
        source_species) - (pm_pt * scalar[1:]))                                 # shape (n_species, n_r-1), kg/s
  out = np.insert(pYk_pt, 0, pT_pt, axis=0)
  out = rearrange(out, 's d -> (s d)')                                          # shape (n_species+1, n_r-1)
  return out


plot_idx = 0
def event_fn(t, y):
  """log and plot the temperature field."""
  y = rearrange(y, '(s d) -> s d', d=cell_R.shape[0])
  print(f"Checkpoint: t = {t:.6e}, "
        f"T = [{', '.join(f'{x:7.2f}' for x in y[0, 0:-1:100])}]")

  # plt.figure(figsize=(6,4))
  global plot_idx
  if plot_idx % 100 == 0:
    fig, ax = plt.subplots(2, 2, figsize=(12, 8))
    ax[0, 0].plot(cell_R / r_min, y[0])
    ax[0, 0].set_xlim([1.0, 15.0])
    ax[0, 0].set_ylim([2500, 4500])
    ax[0, 0].set_xlabel(r'$r/r_p$')
    ax[0, 0].set_ylabel(r'$T$/(K)')
    ax[0, 1].plot(cell_R / r_min, y[2])
    ax[0, 1].set_xlim([1.0, 15.0])
    ax[0, 1].set_ylim([0., 1.])
    ax[0, 1].set_xlabel(r'$r/r_p$')
    ax[0, 1].set_ylabel(r'$\mathrm{AlO}$')
    ax[1, 0].plot(cell_R / r_min, y[-5])
    ax[1, 0].set_xlim([1.0, 15.0])
    ax[1, 0].set_ylim([0., 1.])
    ax[1, 0].set_xlabel(r'$r/r_p$')
    ax[1, 0].set_ylabel(r'$\mathrm{Al_2O_3(L)}$')
    ax[1, 1].plot(cell_R / r_min, y[1])
    ax[1, 1].set_xlim([1.0, 15.0])
    ax[1, 1].set_ylim([0., 1.])
    ax[1, 1].set_xlabel(r'$r/r_p$')
    ax[1, 1].set_ylabel('Al')
    plt.savefig('./al_temprature.pdf', bbox_inches='tight')
    plt.close()
    plt.clf() 
    np.save('./solution.npy', y)
  plot_idx += 1

  return 0  # Trigger event logging

event_fn.terminal = False
event_fn.direction = 0

y0 = rearrange(scalar_gas, 's d -> (s d)')
solution = solve_ivp(
  steady_model,
  t_span=[0, 1e-4],
  y0=y0, # scalar0.flatten(), # x.flatten(),
  method='RK45',
  t_eval=np.arange(0, 1e-4, 1e-6),
  events=event_fn  # Attach event function
)

fig = plt.figure()
ax1 = fig.add_subplot(111)
ax1.plot(solution.t * 1e6, solution.y[0], c='r', ls='-', label=r'$T$')
ax1.set_ylabel('Temperature')
ax1.set_xlabel(r'Time ($\mu$s)')
ax2 = ax1.twinx()  # this is the important function
ax2.plot(solution.t * 1e6, solution.y[0], c='g', ls=':', label='Al')
ax2.plot(solution.t * 1e6, solution.y[1], c='y', ls='-.', label='AlO')
ax2.plot(solution.t * 1e6, solution.y[7], c='c', ls='--', label='Al2O3(l)')
ax2.plot(solution.t * 1e6, solution.y[9], c='b', ls='-', label='O2')
ax2.set_ylabel('Mole fraction')

plt.savefig('test.svg', bbox_inches='tight')
plt.show()

