"""
Single-component droplet diffusion flame simulation
Converted from MATLAB code for m-xylene fuel only
"""

import numpy as np
from scipy.sparse.linalg import spsolve

# Global parameters
class Parameters:
  def __init__(self):
    # Physical constants
    self.kb = 1.3806505e-23  # <PERSON><PERSON>mann constant
    self.Rg = 8.314          # Universal gas constant, J/mol/K
    self.Patm = 101325       # Atmospheric pressure, Pa
    self.Na = 6.02e23        # Avogadro number
    
    # Grid parameters
    self.N = 500             # droplet grid points
    self.rmax = 50           # farthest gas phase position (non-dimensional)
    self.rNum = 200          # number of gas-phase grid points
    
    # Gas phase properties
    self.Y_O_inf = 0.23      # oxygen mass fraction at infinity
    self.Y_N_inf = 1 - self.Y_O_inf  # nitrogen mass fraction at infinity
    self.T_inf = 300         # temperature at infinity, K
    
    # m-Xylene properties (single fuel component)
    self.MW_F1 = 106.16e-3   # kg/mol, molecular weight of m-xylene
    self.MW_O = 32e-3        # kg/mol, molecular weight of O2
    self.MW_N = 28.01e-3     # kg/mol, molecular weight of N2
    self.MW_CO2 = 44e-3      # kg/mol, molecular weight of CO2
    self.MW_H2O = 18e-3      # kg/mol, molecular weight of H2O
    
    self.T_boil_F1 = 139 + 273.15  # K, boiling point of m-xylene
    
    # Chemical formula for m-xylene: C8H10
    self.Chemformu1 = [8, 10, 0]  # C, H, O atoms
    
    # Stoichiometric coefficients for m-xylene combustion
    # C8H10 + 10.5 O2 -> 8 CO2 + 5 H2O
    self.ST11 = self.Chemformu1[0]      # 8 CO2
    self.ST12 = self.Chemformu1[1] / 2  # 5 H2O
    
    # Thermodynamic properties
    self.HvH2O = 40.65e3     # latent heat for H2O, J/mol
    self.Qv1 = 42.65e3 / self.MW_F1  # latent energy for m-xylene evaporation, J/kg
    self.Qc1 = 4.3745e6 / self.MW_F1 + (1/self.MW_F1) * self.ST12 * self.HvH2O  # combustion enthalpy, J/kg
    
    # Stoichiometric oxygen-to-fuel mass ratio
    self.nu1 = (self.Chemformu1[0] + self.Chemformu1[1]/4 - self.Chemformu1[2]/2) * self.MW_O / self.MW_F1
    
    # Liquid phase properties
    self.lambda_l1 = 0.1032  # thermal conductivity of m-xylene, W/m/K
    self.cp_l1 = 184.5 / self.MW_F1  # liquid heat capacity of m-xylene, J/kg/K
    self.rho_l1 = 0.86e3     # liquid density of m-xylene, kg/m3
    
    # Gas phase initial estimates
    self.cp = (2*844 + 3*1930 + 3*4.76*1040) / 9.76 * 5  # J/kg/K gas heat capacity
    self.lambda_g = 0.2      # W/m/K heat conductivity of gas
    
    # Lewis numbers
    self.Le = 10
    self.Le_g = 1
    
    # Initialize grids
    self.setup_grids()

  def setup_grids(self):
    """Setup spatial grids for droplet and gas phase"""
    # Droplet grid (non-dimensional)
    self.xc = np.linspace(0.5/self.N, 1-0.5/self.N, self.N)  # central points
    self.xn = np.linspace(0, 1, self.N+1)  # node points
    self.dx = 1/self.N  # grid size

    # Gas phase grid (non-dimensional)
    self.r = np.linspace(1, self.rmax, self.rNum)
    self.dr = (self.rmax - 1) / (self.rNum - 1)


def liquid_mix_properties(Y_result, T_result, params):
  """
  Calculate droplet liquid properties for m-xylene.
  """
  # 1. Liquid density for m-xylene (temperature dependent)
  rho_l1 = 995.58 - 0.1449 * T_result - 0.0011 * T_result**2
  rho_d= rho_l1

  # 2. Heat capacity for m-xylene (temperature dependent)
  T_K = T_result
  # Coefficients for C8H10
  a = np.array([3.8452, 1.9570, 3.6968])
  b = np.array([-0.33997, -0.31938, -1.6037])
  d = np.array([0.19489, 0.11911, 0.55022])
  n = np.array([2, 2, 4])
  A = np.sum(a * n)  # = 26.3916
  B = np.sum(b * n)  # = -7.7335
  D = np.sum(d * n)  # = 2.8289

  # Heat capacity formula: cpl = 8.314*(A+B*T/100+D*(T/100)^2)/MW
  # where 8.314 is gas constant R in J/mol/K
  cp_molar = 8.314 * (A + B * T_K/100 + D * (T_K/100)**2)  # J/mol/K
  cp_d = cp_molar / params.MW_F1  # Convert to J/kg/K

  # 3. Thermal conductivity for m-xylene (temperature dependent)
  T_K = T_result
  Tb = 139 + 273.15  # Boiling point in K (412.15 K)
  # CH3 CH= C= group contributions
  Nktck = np.array([0.0141, 0.0082, 0.0143])
  item = np.array([2, 4, 1])  # Number of each group

  # Critical temperature calculation
  Tc = Tb * (0.584 + 0.965 * np.sum(Nktck * item) - (np.sum(Nktck * item))**2)**(-1)

  # Correlation constants
  Astar = 0.00319
  alpha = 1.2
  beta = 0.5
  gamma = 0.167

  # Calculate A parameter
  MW_F1_g = params.MW_F1 * 1e3  # Convert kg/mol to g/mol
  A = Astar * Tb**alpha / MW_F1_g**beta / Tc**gamma

  # Reduced temperature
  Tr = T_K / Tc

  # Thermal conductivity formula: lambda = A*(1-Tr)^0.38/(Tr)^(1/6)
  lambda_d = A * (1 - Tr)**0.38 / Tr**(1/6)

  return rho_d, cp_d, lambda_d


def gas_mix_properties(YF, YO, YP, YN, T, rfp, params):
  """
  Gas mixture properties calculation based on MATLAB Gasmixpro.m
  Exactly matches MATLAB implementation for single-component m-xylene fuel (C8H10)
  """

  # Molecular weights (kg/mol) - exactly as MATLAB
  MW_F1 = params.MW_F1  # m-xylene (C8H10)
  MW_N = 28.01e-3       # N2
  MW_O = 32e-3          # O2
  MW_CO2 = 44e-3        # CO2
  MW_H2O = 18e-3        # H2O

  # For single component m-xylene, epsilon = 1 (only F1, no F2)
  epsilon = 1.0

  # Stoichiometric coefficients for m-xylene combustion
  # C8H10 + 10.5 O2 → 8 CO2 + 5 H2O
  ST11 = params.ST11 * MW_CO2    # CO2 mass per mole of m-xylene
  ST12 = params.ST12 * MW_H2O    # H2O mass per mole of m-xylene
  total_product_mass = ST11 + ST12
  MF_CO2 = ST11 / total_product_mass
  MF_H2O = ST12 / total_product_mass
  XF_CO2 = params.ST11 / (params.ST11 + params.ST12)
  XF_H2O = params.ST12 / (params.ST11 + params.ST12)

  # Calculate mass fractions
  YF1 = YF * epsilon      # All fuel is m-xylene
  YCO2 = YP * MF_CO2
  YH2O = YP * MF_H2O

  # Calculate mole fractions
  total_moles = (YF1/MW_F1 + YO/MW_O + YN/MW_N + YCO2/MW_CO2 + YH2O/MW_H2O)
  XF1 = (YF1/MW_F1) / total_moles
  XO = (YO/MW_O) / total_moles
  XN = (YN/MW_N) / total_moles
  XCO2 = (YCO2/MW_CO2) / total_moles
  XH2O = (YH2O/MW_H2O) / total_moles
  XP = XCO2 + XH2O
  MW_P = (XCO2[0]*MW_CO2 + XH2O[0]*MW_H2O) / (XCO2[0] + XH2O[0])

  # Find flame front position (simplified for our case)
  # rfp = len(T) // 2  # Middle point as approximation

  # Characteristic temperature and components
  ratio1, ratio2, ratio3 = 1/6, 2/3, 1/6
  Tch = ratio1*T[0] + ratio2*T[rfp] + ratio3*T[-1]

  YF1ch = ratio1*YF1[0] + ratio2*YF1[rfp] + ratio3*YF1[-1]
  YOch = ratio1*YO[0] + ratio2*YO[rfp] + ratio3*YO[-1]
  YPch = ratio1*YP[0] + ratio2*YP[rfp] + ratio3*YP[-1]
  YNch = ratio1*YN[0] + ratio2*YN[rfp] + ratio3*YN[-1]
  YCO2ch = YPch * MF_CO2
  YH2Och = YPch * MF_H2O

  # XF1ch = ratio1*XF1[0] + ratio2*XF1[rfp] + ratio3*XF1[-1]
  # XOch = ratio1*XO[0] + ratio2*XO[rfp] + ratio3*XO[-1]
  # XPch = ratio1*XP[0] + ratio2*XP[rfp] + ratio3*XP[-1]
  # XNch = ratio1*XN[0] + ratio2*XN[rfp] + ratio3*XN[-1]
  # XCO2ch = XPch * XF_CO2
  # XH2Och = XPch * XF_H2O

  # Thermal conductivity calculation 
  # Characteristic values
  lambda_g = calculate_gas_thermal_conductivity(YF1ch, YOch, YNch, YCO2ch, YH2Och, Tch)

  # Boundary values
  lambda_bd = calculate_gas_thermal_conductivity(YF1[0], YO[0], YN[0], YCO2[0], YH2O[0], T[0])

  # Specific heat calculation 
  # Characteristic values - convert from molar to mass basis
  cp_F1_molar = specheat_matlab('C8H10', Tch) * MW_F1
  cp_O2_molar = specheat_matlab('O2', Tch) * MW_O
  cp_CO2_molar = specheat_matlab('CO2', Tch) * MW_CO2
  cp_H2O_molar = specheat_matlab('H2O', Tch) * MW_H2O
  cp_N2_molar = specheat_matlab('N2', Tch) * MW_N

  cp_F1 = cp_F1_molar / MW_F1
  cp_O2 = cp_O2_molar / MW_O
  cp_CO2 = cp_CO2_molar / MW_CO2
  cp_H2O = cp_H2O_molar / MW_H2O
  cp_N2 = cp_N2_molar / MW_N

  cp = YF1ch*cp_F1 + YOch*cp_O2 + YNch*cp_N2 + YCO2ch*cp_CO2 + YH2Och*cp_H2O

  # Boundary values
  cp_F1_molar_bd = specheat_matlab('C8H10', T[0]) * MW_F1
  cp_O2_molar_bd = specheat_matlab('O2', T[0]) * MW_O
  cp_CO2_molar_bd = specheat_matlab('CO2', T[0]) * MW_CO2
  cp_H2O_molar_bd = specheat_matlab('H2O', T[0]) * MW_H2O
  cp_N2_molar_bd = specheat_matlab('N2', T[0]) * MW_N

  cp_F1_bd = cp_F1_molar_bd / MW_F1
  cp_O2_bd = cp_O2_molar_bd / MW_O
  cp_CO2_bd = cp_CO2_molar_bd / MW_CO2
  cp_H2O_bd = cp_H2O_molar_bd / MW_H2O
  cp_N2_bd = cp_N2_molar_bd / MW_N

  cp_bd = YF1[0]*cp_F1_bd + YO[0]*cp_O2_bd + YN[0]*cp_N2_bd + YCO2[0]*cp_CO2_bd + YH2O[0]*cp_H2O_bd

  return lambda_g, lambda_bd, cp, cp_bd, MW_P


def heat_transfer(t, T0, rs, alpha_d, lambda_d, params, macro_vars):
  """
  Solve heat transfer equation in the droplet using finite difference method
  Exactly matches MATLAB heattransfer.m implementation
  """
  N = params.N
  xn = params.xn
  dx = params.dx

  # Use macro variables calculated from main simulation (exactly as MATLAB)
  T_inf = params.T_inf
  Y_O_inf = params.Y_O_inf
  Qc = macro_vars['Qc']        # From epsilon*Qc1 + (1-epsilon)*Qc2
  nu = macro_vars['nu']        # From epsilon*nu1 + (1-epsilon)*nu2
  cp = macro_vars['cp']        # From gas_mix_properties
  Bm = macro_vars['Bm']        # From (Y_O_inf/nu+YFs)/(1-YFs)
  Qv = macro_vars['Qv']        # From epsilon*Qv1 + (1-epsilon)*Qv2
  lambda_g = macro_vars['lambda_g']      # From gas_mix_properties
  lambda_bd = macro_vars['lambda_bd']    # From gas_mix_properties
  drs2dt = macro_vars['drs2dt']          # From -2*lambda_g/cp/rho_d*log(1+Bm)

  h1 = (t[-1] - t[0]) / dx
  h2 = (t[-1] - t[0]) / dx**2

  # Avoid division by zero at center (xn[0] = 0) - exactly as MATLAB
  xn_safe = np.where(xn == 0, 1e-10, xn)
  a = -(drs2dt/2/rs**2 * xn + 2*alpha_d/rs**2/xn_safe)
  b = alpha_d/rs**2

  # Boundary condition coefficients (exactly as MATLAB)
  c0 = (T_inf + Y_O_inf*Qc/nu/cp)/Bm * np.log(1+Bm) * lambda_bd/lambda_d[-1] - Qv/cp * np.log(1+Bm) * lambda_g/lambda_d[-1]
  c1 = -np.log(1+Bm)/Bm * lambda_bd/lambda_d[-1]
  
  # Build matrix A and vector B
  A_data = []
  A_row = []
  A_col = []
  B = np.zeros(N+1)
  
  # Boundary condition at center (r=0): dT/dr = 0
  A_data.extend([1, -1])
  A_row.extend([0, 0])
  A_col.extend([0, 1])
  B[0] = 0
  
  # Interior points
  for i in range(1, N):
    A_data.extend([-0.25*a[i]*h1 - 0.5*b[i]*h2, 1 + b[i]*h2, 0.25*a[i]*h1 - 0.5*b[i]*h2])
    A_row.extend([i, i, i])
    A_col.extend([i-1, i, i+1])
    B[i] = (0.25*a[i]*h1 + 0.5*b[i]*h2)*T0[i-1] + (1 - b[i]*h2)*T0[i] + (-0.25*a[i]*h1 + 0.5*b[i]*h2)*T0[i+1]
  
  # Boundary condition at surface
  A_data.extend([-1, 1 - c1*dx])
  A_row.extend([N, N])
  A_col.extend([N-1, N])
  B[N] = c0 * dx
  
  # Solve the system
  from scipy.sparse import csr_matrix
  A = csr_matrix((A_data, (A_row, A_col)), shape=(N+1, N+1))
  T_result = spsolve(A, B)
  
  return T_result


def mass_transfer(t, Y0, rho, rs, D, params, macro_vars):
  """
  Solve mass transfer equation in the droplet for single component
  Exactly matches MATLAB masstransfer.m implementation
  """
  N = params.N
  xn = params.xn
  dx = params.dx

  # Extract Y1 component (m-xylene mass fraction) - exactly as MATLAB
  Y1_0 = Y0[:N+1]  # First N+1 elements are Y1 (m-xylene)
  Y2_0 = 1 - Y1_0  # Y2 = 1 - Y1 (exactly as MATLAB line 9)

  # Use macro variables calculated from main simulation (exactly as MATLAB)
  lambda_g = macro_vars['lambda_g']      # From gas_mix_properties
  lambda_bd = macro_vars['lambda_bd']    # From gas_mix_properties
  cp = macro_vars['cp']                  # From gas_mix_properties
  cp_bd = macro_vars['cp_bd']            # From gas_mix_properties
  drs2dt = macro_vars['drs2dt']          # From -2*lambda_g/cp/rho_d*log(1+Bm)
  Bm = macro_vars['Bm']                  # From (Y_O_inf/nu+YFs)/(1-YFs)
  epsilon = 1.0                          # Single component m-xylene

  h1 = (t[-1] - t[0]) / dx
  h2 = (t[-1] - t[0]) / dx**2

  # Use only the first component of D array and ensure proper dimensions
  D_eff = D[:N+1] if len(D) > N+1 else np.full(N+1, D[0] if len(D) > 0 else 1e-9)

  # Avoid division by zero at center (xn[0] = 0) - exactly as MATLAB
  xn_safe = np.where(xn == 0, 1e-10, xn)
  a = -(drs2dt/2/rs**2 * xn + 2*D_eff/rs**2/xn_safe)
  b = D_eff/rs**2

  # Boundary condition coefficients (exactly as MATLAB)
  B1 = -np.log(1+Bm) * epsilon * lambda_g/cp/rho[-1]/D_eff[-1]
  B0 = -np.log(1+Bm) * lambda_g/cp/D_eff[-1]/rho[-1]
  
  # Build matrix A and vector B
  A_data = []
  A_row = []
  A_col = []
  B = np.zeros(N+1)
  
  # Boundary condition at center
  A_data.extend([1, -1])
  A_row.extend([0, 0])
  A_col.extend([0, 1])
  B[0] = 0
  
  # Interior points (exactly as MATLAB)
  for i in range(1, N):
    A_data.extend([-0.25*a[i]*h1 - 0.5*b[i]*h2, 1 + b[i]*h2, 0.25*a[i]*h1 - 0.5*b[i]*h2])
    A_row.extend([i, i, i])
    A_col.extend([i-1, i, i+1])
    B[i] = (0.25*a[i]*h1 + 0.5*b[i]*h2)*Y1_0[i-1] + (1 - b[i]*h2)*Y1_0[i] + (-0.25*a[i]*h1 + 0.5*b[i]*h2)*Y1_0[i+1]

  # Boundary condition at surface (exactly as MATLAB lines 34-37)
  A_data.extend([1/2, -2, 3/2 + B0*dx])
  A_row.extend([N, N, N])
  A_col.extend([N-2, N-1, N])
  B[N] = dx * B1

  # Solve the system
  from scipy.sparse import csr_matrix
  A = csr_matrix((A_data, (A_row, A_col)), shape=(N+1, N+1))
  Y1_result = spsolve(A, B)

  # Ensure mass fractions are physical (between 0 and 1)
  Y1_result = np.clip(Y1_result, 0, 1)

  # For single component, Y2 = 1 - Y1, but since we only have m-xylene, Y1 should be close to 1
  Y2_result = 1 - Y1_result
  Y_result = np.concatenate([Y1_result, Y2_result])

  return Y_result


def main_simulation():
  """
  Main simulation function for single-component droplet diffusion flame

  Parameters:
  quick_test: bool, if True run a shorter simulation for testing
  """
  # Initialize parameters
  params = Parameters()

  # Time grid
  tspan = np.linspace(0, 25e-3, int(1e4) + 1)  # 0 to 25 ms

  # Initial gas conditions
  Ts = 300  # surface temperature (K)
  rs0 = 50e-6  # initial droplet radius (m)
  rs = rs0
  rho_d = params.rho_l1  # initial droplet density
  MW_P = params.MW_N  # initial product molecular weight
  T = np.ones(params.rNum) * 300  # initial gas temperature
  # Initial conditions for gas phase (physical estimates, not final values)
  # These are initial guesses that will be updated during iteration
  XNs = 1.0  # initial nitrogen mole fraction (air composition, physically reasonable)
  XPs = 0.0   # initial product mole fraction (no combustion products initially)

  # Initial droplet conditions
  T0 = np.ones(params.N + 1) * Ts  # initial temperature
  Y0 = np.zeros(2 * (params.N + 1))  # initial mass fractions
  # For single component (m-xylene only), set Y1 = 1, Y2 = 0
  Y0[:params.N + 1] = 1.0  # m-xylene mass fraction = 1
  Y0[params.N + 1:] = 0.0  # no second component

  Tinit = T0.copy()
  Yinit = Y0.copy()

  # Storage arrays
  Time_history = []
  Tresult_history = []
  Tgresult_history = []
  Y1result_history = []
  YFresult_history = []
  rs_history = []
  rf_history = []
  drs2dt_history = []
  B_history = []

  print("Starting single-component droplet diffusion flame simulation...")
  print(f"Initial droplet radius: {rs0*1e6:.1f} μm")

  j0 = 5  # number of inner iterations

  for i in range(len(tspan) - 1):
    for j in range(1, j0 + 1):
      # Liquid T and c for the gas-phase boundary condition
      Ts = Tinit[-1]  # droplet surface temperature
      Y1s = Yinit[params.N]  # m-xylene mass fraction at surface

      # For single component, mole fraction = mass fraction
      X1s = Y1s  # m-xylene mole fraction at surface

      # Clausius-Clapeyron equation for vapor pressure
      XF1s = X1s * np.exp(params.Qv1 * params.MW_F1 / params.Rg *
                          (1/params.T_boil_F1 - 1/Ts))

      # Gas phase mass fractions at surface
      YF1s = XF1s * params.MW_F1 / (XF1s * params.MW_F1 + XNs * params.MW_N + XPs * params.MW_CO2)
      YFs = YF1s  # total fuel mass fraction
      # print(f"YF1s = {YF1s:.4f}")
      epsilon = 1.0  # single component

      # Product molecular weight (simplified)
      MW_P = (params.ST11 * params.MW_CO2 + params.ST12 * params.MW_H2O) / (params.ST11 + params.ST12)
      MW_F = params.MW_F1  # fuel molecular weight

      # Gas phase temperature and species profile
      nu = params.nu1  # stoichiometric oxygen-to-fuel mass ratio
      Qc = params.Qc1  # combustion heat
      Qv = params.Qv1  # vaporization heat
      Bm = (params.Y_O_inf/nu + YFs) / (1 - YFs)  # Spalding transfer number

      # Critical combustion limit
      CombCri = rs**2 / rs0**2 * 100
      CombCri0 = 0

      if CombCri < CombCri0:
        rf = 1
      else:
        rf = np.log(1 + Bm) / np.log(1 + params.Y_O_inf/nu)  # flame front position

        # Initialize species arrays
        YF = np.zeros(params.rNum)
        YO = np.zeros(params.rNum)
        YP = np.zeros(params.rNum)
        YN = np.zeros(params.rNum)

        # Find indices for flame regions
        inside_flame = params.r < rf
        outside_flame = params.r >= rf

        # Fuel species distribution
        if np.any(inside_flame):
            r_in = params.r[inside_flame]
            YF[inside_flame] = (-params.Y_O_inf/nu + (YFs + params.Y_O_inf/nu) *
                                (np.exp(-1/r_in * np.log(1 + Bm)) - 1) / (1/(1 + Bm) - 1))
        YF[outside_flame] = 0

        # Oxygen distribution
        YO[inside_flame] = 0
        if np.any(outside_flame):
            r_out = params.r[outside_flame]
            YO[outside_flame] = (params.Y_O_inf - (YFs * nu + params.Y_O_inf) *
                                (np.exp(-1/r_out * np.log(1 + Bm)) - 1) / (1/(1 + Bm) - 1))

        # Product distribution
        if np.any(inside_flame):
            r_in = params.r[inside_flame]
            YP[inside_flame] = ((nu + 1)/nu * params.Y_O_inf -
                                (nu + 1)/nu * Bm/(1 + Bm) * params.Y_O_inf *
                                (np.exp(-1/r_in * np.log(1 + Bm)) - 1) / (1/(1 + Bm) - 1))

        if np.any(outside_flame):
            r_out = params.r[outside_flame]
            YP[outside_flame] = (((nu + 1) * YFs + (1 + nu)/nu/(1 + Bm) * params.Y_O_inf) *
                                (np.exp(-1/r_out * np.log(1 + Bm)) - 1) / (1/(1 + Bm) - 1))

        # Nitrogen distribution
        YN = (params.Y_N_inf - Bm/(1 + Bm) * params.Y_N_inf *
              (np.exp(-1/params.r * np.log(1 + Bm)) - 1) / (1/(1 + Bm) - 1))

      # Update surface values
      YFs = YF[0] if len(YF) > 0 else YFs
      YOs = YO[0] if len(YO) > 0 else 0
      YPs = YP[0] if len(YP) > 0 else 0
      YNs = YN[0] if len(YN) > 0 else params.Y_N_inf

      # Update mole fractions
      XPs = YPs/MW_P / (YFs/MW_F + YOs/params.MW_O + YPs/MW_P + YNs/params.MW_N)
      XNs = YNs/params.MW_N / (YFs/MW_F + YOs/params.MW_O + YPs/MW_P + YNs/params.MW_N)

      # Physical parameter estimation
      rfp = int(np.ceil((rf - 1)/params.dr)) + 1
      rfp = min(rfp, params.rNum - 1)  # ensure within bounds

      lambda_g, lambda_bd, cp, cp_bd, MW_P = gas_mix_properties(YF, YO, YP, YN, T, rfp, params)

      # Temperature distribution
      if np.any(inside_flame):
          r_in = params.r[inside_flame]
          T[inside_flame] = (params.T_inf + params.Y_O_inf * Qc/nu/cp +
                            (Ts - params.T_inf - params.Y_O_inf * Qc/nu/cp) *
                            (np.exp(-1/r_in * np.log(1 + Bm)) - 1) / (1/(1 + Bm) - 1))

      if np.any(outside_flame):
          r_out = params.r[outside_flame]
          T[outside_flame] = (params.T_inf + (Ts - params.T_inf + YFs * Qc/cp) *
                              (np.exp(-1/r_out * np.log(1 + Bm)) - 1) / (1/(1 + Bm) - 1))

      # Droplet regression rate (exactly as MATLAB)
      # Handle both scalar and array cases for rho_d
      rho_surface = rho_d[-1] if hasattr(rho_d, '__len__') else rho_d
      drs2dt = -2 * lambda_g/cp/rho_surface * np.log(1 + Bm)

      # Create macro variables dictionary (exactly as MATLAB global variables)
      macro_vars = {
        'Qc': Qc,                    # combustion heat
        'nu': nu,                    # stoichiometric ratio
        'cp': cp,                    # gas specific heat (characteristic)
        'cp_bd': cp_bd,              # gas specific heat (boundary)
        'Bm': Bm,                    # Spalding transfer number
        'Qv': Qv,                    # vaporization heat
        'lambda_g': lambda_g,        # gas thermal conductivity (characteristic)
        'lambda_bd': lambda_bd,      # gas thermal conductivity (boundary)
        'drs2dt': drs2dt,            # droplet regression rate
        'epsilon': epsilon           # fuel composition parameter
      }

      # Mass and heat diffusion in liquid
      converge = True
      Tresult_temp = Tinit.copy()
      Yresult_temp = Yinit.copy()
      iteration_count = 0
      max_iterations = 10

      while converge and iteration_count < max_iterations:
        # Estimate liquid properties
        rho_d, cp_d, lambda_d = liquid_mix_properties(Yresult_temp, Tresult_temp, params)
        alpha_d = lambda_d / cp_d / rho_d  # thermal diffusivity
        D = alpha_d / params.Le  # mass diffusivity
        D = np.concatenate([D, D])  # for both components

        # Solve heat transfer (with macro variables)
        t_sub = np.linspace(tspan[i] * (j0 + 1 - j)/j0 + tspan[i+1] * (j - 1)/j0, tspan[i+1], 3)
        Tresult = heat_transfer(t_sub, Tinit, rs, alpha_d, lambda_d, params, macro_vars)

        # Solve mass transfer (with macro variables)
        Yresult = mass_transfer(t_sub, Yinit, rho_d, rs, D, params, macro_vars)

        Y1result = Yresult[:params.N + 1]  # m-xylene mass fraction

        # Check convergence
        errorT = np.sum(np.abs(Tresult - Tresult_temp)) / np.sum(np.abs(Tresult_temp))
        errorY = np.sum(np.abs(Yresult - Yresult_temp)) / np.sum(np.abs(Yresult_temp))

        if errorT < 1e-7 and errorY < 1e-7:
          converge = False
        else:
          Tresult_temp = Tresult.copy()
          Yresult_temp = Yresult.copy()
          iteration_count += 1

      # Update initial conditions
      Tinit = Tresult.copy()
      Yinit = Yresult.copy()

      # Apply relaxation for stability
      if j != j0:
        jj = (j0 - j) / (j0 + 1 - j)
        Tinit = jj * Tresult + (1 - jj) * Tinit
        Yinit = jj * Yresult + (1 - jj) * Yinit

    # Save results to history
    YFresult_history.append([YF1s, 0, YF1s])  # [YF1s, YF2s=0, total]
    rf_history.append(rf)
    Tgresult_history.append(T.copy())
    Tresult_history.append(Tresult.copy())
    Y1result_history.append(Y1result.copy())
    drs2dt_history.append(drs2dt)
    B_history.append(Ts)
    Time_history.append(tspan[i+1])

    # Solve droplet shrinkage
    dmdt = 4 * np.pi * rs * lambda_g/cp * np.log(1 + Bm) / params.Le_g
    rs2 = rs**2 + drs2dt * (tspan[i+1] - tspan[i])

    if rs2 > 0:
      rs = np.sqrt(rs2)
      rs_history.append(rs)
    else:
      print(f"Droplet completely evaporated at step {i}")
      break

    # break
    if rs2/rs0**2 < 0.9:
      print(f"Reached target size (r²/r₀² = 0.9) at step {i}")
      break

    # Progress output
    if i % 1000 == 0:
        print(f'Step = {i}, r² = {rs**2/rs0**2*100:.2f}%, Bm = {Bm:.3e}')

  # Store final gas phase profiles for plotting
  final_gas_profiles = {
    'YF': YF if 'YF' in locals() else np.zeros(params.rNum),
    'YO': YO if 'YO' in locals() else np.ones(params.rNum) * params.Y_O_inf,
    'YP': YP if 'YP' in locals() else np.zeros(params.rNum),
    'YN': YN if 'YN' in locals() else np.ones(params.rNum) * params.Y_N_inf,
    'T': T if 'T' in locals() else np.ones(params.rNum) * params.T_inf,
    'rf': rf if 'rf' in locals() else 1.0
  }

  # Compile results
  results = {
    'time': np.array(Time_history),
    'radius': np.array(rs_history),
    'flame_front': np.array(rf_history),
    'temperature_gas': np.array(Tgresult_history),
    'temperature_droplet': np.array(Tresult_history),
    'fuel_mass_fraction': np.array(Y1result_history),
    'fuel_gas_fraction': np.array(YFresult_history),
    'spalding_number': np.array([]),  # Would need to store Bm values
    'surface_temperature': np.array(B_history),
    'regression_rate': np.array(drs2dt_history),
    'radial_grid_gas': params.r,
    'radial_grid_droplet': params.xn,
    'initial_radius': rs0,
    'final_gas_profiles': final_gas_profiles
  }

  print(f"\nSimulation completed!")
  print(f"Final droplet radius: {rs*1e6:.1f} μm")
  print(f"Simulation time: {Time_history[-1]*1000:.2f} ms")

  return results, params


def plot_final_profiles(results, params):
  """
  Plot final gas phase profiles: fuel, oxidizer, inert, product, and temperature
  """
  import matplotlib.pyplot as plt

  # Extract final profiles
  profiles = results['final_gas_profiles']
  r = params.r
  rf = profiles['rf']

  # Create the plot
  fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(7, 3))

  # Plot 1: Species mass fractions
  ax1.semilogx(r, profiles['YF'], 'r-', linewidth=1, label=r'Fuel ($\mathrm{m-Xylene}$)')
  ax1.semilogx(r, profiles['YO'], 'b-', linewidth=1, label=r'Oxidizer ($\mathrm{O_2}$)')
  ax1.semilogx(r, profiles['YN'], 'g-', linewidth=1, label=r'Inert ($\mathrm{N_2}$)')
  ax1.semilogx(r, profiles['YP'], 'm-', linewidth=1, label=r'Product ($\mathrm{CO_2}+\mathrm{H_2O}$)')

  # Mark flame front on both plots (but only add to legend once)
  ax1.axvline(rf, color='orange', linestyle='--', linewidth=1, alpha=0.7)

  ax1.set_xlabel(r'Dimensionless radial position ($r/r_s$)')
  ax1.set_ylabel('Species mass fraction')
  ax1.grid(True, alpha=0.3)
  ax1.set_xlim([1, params.rmax])
  ax1.set_ylim([0, max(1.1*np.max(profiles['YN']), 0.3)])

  # Plot 2: Temperature profile
  ax2.semilogx(r, profiles['T'], 'orange', linewidth=1, label='Temperature')

  # Mark flame front
  ax2.axvline(rf, color='orange', linestyle='--', linewidth=1, alpha=0.7,
              label='Flame front') #  (r/rs = {rf:.2f})

  ax2.set_xlabel(r'Dimensionless radial position ($r/r_s$)')
  ax2.set_ylabel('Temperature (K)')
  ax2.grid(True, alpha=0.3)
  ax2.set_xlim([1, params.rmax])

  # Set a combined title for the entire figure
  fig.suptitle( r'Gas Phase Species and Temperature Profiles at $r^2/r_0^2 = 0.9$', fontsize=12)

  # Collect all legend handles and labels from both subplots
  handles1, labels1 = ax1.get_legend_handles_labels()
  handles2, labels2 = ax2.get_legend_handles_labels()

  # Combine all handles and labels
  all_handles = handles1 + handles2
  all_labels = labels1 + labels2

  # Create a single legend at the bottom of the figure
  fig.legend(all_handles, all_labels, loc='lower center', bbox_to_anchor=(0.5, -0.2),
             ncol=3, frameon=True, fancybox=False, shadow=False)

  plt.tight_layout()
  # Adjust layout to make room for the legend at the bottom
  plt.subplots_adjust(bottom=0.15)
  plt.savefig('./figs/diffusion_flame_profiles.pdf', bbox_inches='tight')
  plt.savefig('./figs/diffusion_flame_profiles.svg', bbox_inches='tight')
  plt.show()

  # Print summary information
  print(f"\nFinal Simulation Results (at r²/r₀² = 0.9):")
  print(f"=" * 50)
  print(f"Final droplet radius: {results['radius'][-1]*1e6:.1f} μm")
  print(f"Initial droplet radius: {results['initial_radius']*1e6:.1f} μm")
  print(f"Simulation time: {results['time'][-1]*1000:.2f} ms")
  print(f"Flame front position: {rf:.2f} (r/rs)")
  print(f"Surface temperature: {results['surface_temperature'][-1]:.1f} K")

  print(f"\nSpecies at droplet surface (r/rs = 1):")
  print(f"Fuel mass fraction: {profiles['YF'][0]:.4f}")
  print(f"Oxidizer mass fraction: {profiles['YO'][0]:.4f}")
  print(f"Inert mass fraction: {profiles['YN'][0]:.4f}")
  print(f"Product mass fraction: {profiles['YP'][0]:.4f}")
  print(f"Temperature: {profiles['T'][0]:.1f} K")

  print(f"\nSpecies at infinity (r/rs → ∞):")
  print(f"Fuel mass fraction: {profiles['YF'][-1]:.4f}")
  print(f"Oxidizer mass fraction: {profiles['YO'][-1]:.4f}")
  print(f"Inert mass fraction: {profiles['YN'][-1]:.4f}")
  print(f"Product mass fraction: {profiles['YP'][-1]:.4f}")
  print(f"Temperature: {profiles['T'][-1]:.1f} K")


def specheat_matlab(species, T):
  """
  Gas phase specific heat calculation based on exact MATLAB specheat.m
  Uses NASA polynomial coefficients with temperature-dependent selection
  """
  R = 8.314  # J/mol/K

  # Determine high or low temperature coefficients
  if T > 1000:
      crit = 'h'  # High temperature
  else:
      crit = 'l'  # Low temperature

  if species == 'C8H10':  # m-xylene
      if crit == 'h':
          # High temperature coefficients (T > 1000K)
          coeffs = np.array([1.09952650E+01, 3.94471414E-02, -1.60141065E-05,
                            3.02159851E-09, -2.15018598E-13])
      else:
          # Low temperature coefficients (T <= 1000K)
          coeffs = np.array([1.41688302E+00, 4.24899260E-02, 2.90259016E-05,
                            -5.79774769E-08, 2.22785462E-11])
      MW = 106.16e-3  # kg/mol

  elif species == 'O2':
      if crit == 'h':
          coeffs = np.array([3.28253784E+00, 1.48308754E-03, -7.57966669E-07,
                            2.09470555E-10, -2.16717794E-14])
      else:
          coeffs = np.array([3.78245636E+00, -2.99673416E-03, 9.84730201E-06,
                            -9.68129509E-09, 3.24372837E-12])
      MW = 32e-3  # kg/mol

  elif species == 'N2':
      if crit == 'h':
          # High temperature coefficients (T > 1000K)
          coeffs = np.array([2.92664000E+00, 1.48797680E-03, -5.68476000E-07,
                            1.00970380E-10, -6.75335100E-15])
      else:
          # Low temperature coefficients (T <= 1000K) - corrected
          coeffs = np.array([3.29867700E+00, 1.40824040E-03, -3.96322200E-06,
                            5.64151500E-09, -2.44485400E-12])
      MW = 28.01e-3  # kg/mol

  elif species == 'CO2':
      if crit == 'h':
          coeffs = np.array([3.85746029E+00, 4.41437026E-03, -2.21481404E-06,
                            5.23490188E-10, -4.72084164E-14])
      else:
          coeffs = np.array([2.35677352E+00, 8.98459677E-03, -7.12356269E-06,
                            2.45919022E-09, -1.43699548E-13])
      MW = 44e-3  # kg/mol

  elif species == 'H2O':
      if crit == 'h':
          coeffs = np.array([3.03399249E+00, 2.17691804E-03, -1.64072518E-07,
                            -9.70419870E-11, 1.68200992E-14])
      else:
          coeffs = np.array([4.19864056E+00, -2.03643410E-03, 6.52040211E-06,
                            -5.48797062E-09, 1.77197817E-12])
      MW = 18e-3  # kg/mol

  elif species == 'C8H16O2':  # 2-ethylhexanoic acid
      # Using approximation based on similar organic compounds
      # Since exact NASA coefficients not available, use reasonable estimates
      if crit == 'h':
          coeffs = np.array([1.50000000E+01, 4.50000000E-02, -1.80000000E-05,
                            3.50000000E-09, -2.50000000E-13])
      else:
          coeffs = np.array([2.00000000E+00, 5.00000000E-02, 3.00000000E-05,
                            -6.00000000E-08, 2.50000000E-11])
      MW = 144e-3  # kg/mol

  else:
      # Default case
      return 1000  # J/kg/K

  # Calculate cp using NASA polynomial: cp = R*(a1 + a2*T + a3*T^2 + a4*T^3 + a5*T^4)
  cp_molar = R * (coeffs[0] + coeffs[1]*T + coeffs[2]*T**2 +
                  coeffs[3]*T**3 + coeffs[4]*T**4)

  # Convert from J/mol/K to J/kg/K
  cp_mass = cp_molar / MW

  return cp_mass


def calculate_gas_thermal_conductivity(YF, YO, YN, YCO2, YH2O, T):
  """
  Gas thermal conductivity calculation based on MATLAB lamb.m
  Uses kinetic theory with Lennard-Jones parameters
  Merged function without second fuel and zero checks
  """
  # Physical constants
  kb = 1.38064852e-23  # Boltzmann constant (J/K)
  Na = 6.02214076e23   # Avogadro number (1/mol)
  Patm = 101325        # Atmospheric pressure (Pa)
  Rg = 8.314           # Gas constant (J/mol/K)
  pi = np.pi

  # Species properties - Order: C8H10, CO2, O2, N2, H2O (5 species)
  MW = np.array([106, 44, 32, 28, 18]) / 1e3  # kg/mol
  sigma_k = np.array([6.51, 3.76, 3.46, 3.62, 2.60]) * 1e-10  # m
  epsilon = np.array([4003.83, 2028.74, 892.98, 810.91, 4759.22]) / Na  # J
  Z_rot_0 = np.array([0.00, 2.10, 3.80, 4.00, 4.00])

  # Calculate mass fractions array (5 species)
  Y = np.array([YF, YCO2, YO, YN, YH2O])

  # Calculate mole fractions
  total_moles = np.sum(Y / MW)
  X = (Y / MW) / total_moles

  # Calculate specific heats for each species
  C_v = np.zeros(5)
  C_v[0] = specheat_matlab('C8H10', T) * MW[0] - Rg    # C8H10
  C_v[1] = specheat_matlab('CO2', T) * MW[1] - Rg      # CO2
  C_v[2] = specheat_matlab('O2', T) * MW[2] - Rg       # O2
  C_v[3] = specheat_matlab('N2', T) * MW[3] - Rg       # N2
  C_v[4] = specheat_matlab('H2O', T) * MW[4] - Rg      # H2O

  # Reduced temperature
  T_s = kb * T / epsilon

  # Collision integrals
  Omega_D = (1.06036 / T_s**0.15610 + 0.19300 / np.exp(0.47635 * T_s) +
              1.03587 / np.exp(1.52996 * T_s) + 1.76474 / np.exp(3.89411 * T_s))

  Omega_22 = (1.16145 / T_s**0.14874 + 0.52487 / np.exp(0.7732 * T_s) +
              2.16178 / np.exp(2.43787 * T_s) -
              6.435e-4 * T_s**0.14874 * np.sin(18.0323 * T_s**(-0.7683) - 7.27371))

  # Density and diffusion coefficients
  rho = Patm * MW / Rg / T
  D_kk = (3/8 * np.sqrt(pi * kb**3 * T**3 / MW * Na) / Patm / pi /
          sigma_k**2 / Omega_D)

  # Viscosity
  eta_k = (5/16 * np.sqrt(pi * MW / Na * kb * T) / pi /
            sigma_k**2 / Omega_22)

  # F function as lambda
  F_func = lambda T_val, eps: (1 + pi**(3/2)/2 * (eps / kb / T_val)**(1/2) +
                              (pi**2/4 + 2) * (eps / kb / T_val) +
                              pi**(3/2) * (eps / kb / T_val)**(3/2))

  # Thermal conductivity components
  C_v_trans = 1.5 * Rg
  C_v_rot = np.array([1.5, 1, 1, 1, 1.5]) * Rg  # C8H10, CO2, O2, N2, H2O
  C_v_vib = C_v - np.array([3, 2.5, 2.5, 2.5, 3]) * Rg

  # Rotational collision number correction
  F_298 = F_func(298, epsilon)
  F_T = F_func(T, epsilon)
  Z_rot = Z_rot_0 * F_298 / F_T

  # Thermal conductivity calculation parameters
  A = 5/2 - rho * D_kk / eta_k
  B = Z_rot + 2/pi * (5/3 * C_v_rot / Rg + rho * D_kk / eta_k)

  # Thermal conductivity factors
  f_trans = 5/2 * (1 - 2/pi * C_v_rot / C_v_trans * A / B)
  f_rot = rho * D_kk / eta_k * (1 + 2/pi * A / B)
  f_vib = rho * D_kk / eta_k

  # Individual species thermal conductivities
  lambda_k = eta_k / MW * (f_trans * C_v_trans + f_rot * C_v_rot + f_vib * C_v_vib)

  # Mixture thermal conductivity (harmonic-arithmetic mean)
  arithmetic_mean = np.sum(X * lambda_k)
  harmonic_mean = 1.0 / np.sum(X / lambda_k)
  lambda_mix = 0.5 * (arithmetic_mean + harmonic_mean)

  return lambda_mix


if __name__ == "__main__":
    print("Running single-component droplet simulation until r²/r₀² = 0.9...")
    results, params = main_simulation()

    # Plot the final profiles
    plot_final_profiles(results, params)
