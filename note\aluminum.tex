\documentclass[10pt]{article}
\usepackage[english]{babel}
\usepackage[utf8x]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{scribe}
\usepackage{listings}
\usepackage{nomencl}
\makenomenclature
\usepackage{color}
\usepackage[colorlinks, linkcolor=blue, anchorcolor=blue, citecolor=magenta]{hyperref}
%% Math
\usepackage{mathtools}
\usepackage[version=4]{mhchem}
\usepackage{bbm}
\usepackage{siunitx} % 国际单位制
\usepackage{amsmath}
\numberwithin{equation}{section}
%% Figure
\usepackage{graphicx}  %插入图片的宏包
\usepackage{float}  %设置图片浮动位置的宏包
\usepackage{subfigure}  %插入多图时用子图显示的宏包
\usepackage[skip=2pt]{caption} % example skip set to 2pt
%% Table
\usepackage{booktabs} % 表格横线
\usepackage{multirow}
%% Bibliography
\usepackage{natbib}
\usepackage{enumerate}[itemindent=4em]

\Scribe{Shen Fang}
\Lecturer{Siyi Zhang}
\LectureDate{\today}
\LectureTitle{Aluminum Combustion Instability}

\lstset{style=mystyle}

\begin{document}
\numberwithin{equation}{section}
\MakeScribeTop
\setlength{\abovedisplayskip}{5pt}
\setlength{\belowdisplayskip}{5pt}
\tableofcontents

%#############################################################
%#############################################################
\newpage
\section{Aluminum Reaction Mechanism}

Reaction mechanisms form the foundation for simulating aluminum combustion in various atmospheres and environments. The primary reaction mechanisms for aluminum can be categorized into two types: (1) gas-phase mechanisms, which describe the reactions of vapor-phase aluminum and aluminum oxide with different reactive gases, and (2) surface mechanisms, which describe the reactions of bulk and liquid aluminum with reactive gases in the surrounding environment. Reactions occurring within the bulk aluminum are typically neglected, with only the melting of aluminum into its liquid phase being considered.

The gas-phase and surface reaction mechanisms used in this study are derived from the work of the Laurent Catoire group \citep{swihart2000thermochemistry, catoire2003kinetic, glorian2014cinetique, glorian2015gas, glorian2016role}. To minimize computational complexity, a reduced mechanism from \citep{hu2021simulation} is employed, focusing exclusively on the reactions between aluminum and aluminum oxide species with oxygen.

\subsection{Gas-phase mechanism}
The reduced gas-phase mechanism is detailed in \hyperref[table:gas_mech]{Table 1}.
\begin{table}[ht]
    \centering
    \begin{tabular}{ccccc}
    \toprule
    \textbf{Index} & \textbf{Reaction} & $\mathbf{A(cm-mol-s-K)}$ & $\mathbf{b}$ & $\mathbf{E(K)}$ \\ \midrule
    1 & \ce{Al + O <-> AlO} & 1.00E13 & 0 & 800 \\
    2 & \ce{Al + O2 <-> AlO + O} & 9.72E13 & 0 & 80.5 \\
    3 & \ce{Al + O + M <-> AlO + M (O2 1.1)} & 3.00E17 & -1 & 0 \\
    4 & \ce{AlO + O2 <-> AlO2 + O} & 4.62E14 & 0 & 10,008 \\
    5 & \ce{O2 + M <-> O + O + M} & 1.20E14 & 0 & 54,244 \\
    6 & \ce{Al2O3 <-> Al2O2 + O} & 3.00E15 & 0 & 49,144.4 \\
    7 & \ce{Al2O3 <-> AlO2 + AlO} & 3.00E15 & 0 & 63,915.4 \\
    8 & \ce{Al2O3c <-> Al2O2 + O} & 3.00E15 & 0 & 36,864.6 \\
    9 & \ce{Al2O3c <-> AlO2 + AlO} & 3.00E15 & 0 & 51,635.6 \\
    10 & \ce{Al2O2 <-> AlO + AlO} & 1.00E15 & 0 & 59,335.7 \\
    11 & \ce{Al2O2 <->  Al + AlO2} & 1.00E15 & 0 & 74,937.1 \\
    12 & \ce{Al2O2 <->  Al2O + O} & 1.00E15 & 0 & 52,466 \\
    13 & \ce{AlO2 <-> AlO + O} & 1.00E15 & 0 & 44,564.6 \\
    14 & \ce{Al2O <-> AlO + Al} & 1.00E15 & 0 & 67,035.7 \\
    15 & \ce{Al2O3 -> Al2O3(l)} & 1.00E15 & 0 & 0 \\
    16 & \ce{Al2O3c -> Al2O3(l)} & 1.00E15 & 0 & 0 \\ \bottomrule
    \end{tabular}
    \caption{Gas-phase mechanism}
    \label{table:gas_mech}
\end{table}

Here, \ce{Al2O3c} refers to \ce{Al2O3} with a planar structure, as studied in \citep{swihart2000thermochemistry, saba2021reaction}. This structure exhibits $C_{2v}$ symmetry and has an energy approximately 11 kcal/mol higher than that of the linear isomer.

The thermochemical properties, recorded using NASA coefficients, are primarily sourced from the recent work of \citep{saba2021reaction}. This study provides a detailed gas-phase reaction mechanism for the \ce{Al}-\ce{O2} system, along with comprehensive NASA coefficients for aluminum and aluminum oxide species in the mechanism file. The parameters for liquid aluminum oxide, \ce{Al2O3(l)}, are adopted from \citep{glorian2014cinetique}.

For transport properties, the parameters are taken from \citep{hu2021simulation, starik2014numerical}. Notably, the transport properties of the condensed phase \ce{Al2O3(l)} are assumed to be identical to those of its gas phase, as the particle size of the generated alumina smoke is small ($\sim 1 \mu m$) \citep{huang2009effect}. It is important to note that the molecular geometry of \ce{Al2O3c} in \citep{hu2021simulation} is incorrect; it should be described as a planar geometry, with the corresponding value in the mechanism set to 2.

\subsection{Surface mechanism} \label{sec:1.2}
The reduced surface mechanism is detailed in \hyperref[table:surf_mech]{Table 2}.
\begin{table}[ht]
    \centering
    \begin{tabular}{@{}ccccc@{}}
    \toprule
    \textbf{Index} & \textbf{Reaction} & $\mathbf{A(cm-mol-s-K)}$ or $\gamma$ & $\mathbf{b}$ & $\mathbf{E(K)}$ \\ \midrule
    1 & \begin{tabular}[c]{@{}c@{}} \ce{Al + Al(L) <-> Al(B) + Al(L)} \\ STICK\end{tabular} & 1.00 & 0 & 0 \\
    2 & \begin{tabular}[c]{@{}c@{}} \ce{O + Al(L) <-> O(S) + Al(B)} \\ STICK\end{tabular} & 1.00 & 0 & 0 \\
    3 & \begin{tabular}[c]{@{}c@{}} \ce{O2 + 2Al(L) <-> 2O(S) + 2Al(B)} \\ STICK\end{tabular} & 5.00E-3 & 0 & 0 \\
    4 & \begin{tabular}[c]{@{}c@{}} \ce{Al(L) + AlO <-> AlO(S) + Al(B)} \\ STICK\end{tabular} & 1.00 & 0 & 0 \\
    5 & \ce{AlO(S) + Al(L) <-> Al(L) + O(S) + Al(B)} & 2.26E19 & 0 & 1424.1 \\
    6 & \ce{Al(L) + O <-> AlO(S)} & 2.29E13 & 0 & 0 \\
    7 & \begin{tabular}[c]{c} \ce{Al2O + 3Al(L) <-> Al2O(S) + 3Al(B)} \\ STICK\end{tabular} & 1.00 & 0 & 0 \\
    8 & \ce{AlO(S) + Al + 2Al(L) <-> Al2O(S) + 2Al(B)} & 2.29E13 & 0 & 0 \\
    9 & \ce{AlO + Al(L) + 2Al(L) <-> Al2O(S) + 2Al(B)} & 2.29E13 & 0 & 0 \\
    10 & \ce{Al(L) + AlO(S) + Al(L) <-> Al2O(S)  + Al(B)} & 2.26E19 & 0 & 0 \\
    11 & \ce{2Al(L) + O + Al(L) <-> Al2O(S) + Al(B)} & 2.29E13 & 0 & 0 \\
    12 & \begin{tabular}[c]{@{}c@{}} \ce{AlO2 + Al(L) <-> AlO2(S) + Al(B)} \\ STICK\end{tabular} & 0.75 & 0 & 0 \\
    13 & \ce{2O(S) + A1 + Al(B) <-> AlO2(S) + Al(L)} & 2.29E13 & 0 & 0 \\
    14 & \ce{AlO + O(S) <-> AlO2(S)} & 2.29E13 & 0 & 0 \\
    15 & \begin{tabular}[c]{c} \ce{Al2O2 + 3Al(L) <-> Al2O2(S) + 3Al(B)} \\ STICK\end{tabular} & 0.75 & 0 & 0 \\
    16 & \ce{AlO2 + Al(L) + 2Al(L) <-> Al2O2(S) + 2Al(B)} & 2.29E13 & 0 & 0 \\
    17 & \ce{AlO(S) + AlO + 2Al(L) <-> Al2O2(S) + 2Al(B)} & 2.29E13 & 0 & 0 \\
    18 & \ce{Al2O + O(S) + 2Al(L) <-> Al2O2(S) + 2Al(B)} & 2.29E13 & 0 & 5664.4 \\
    19 & \ce{Al2O(S) + O <-> Al2O2(S)} & 2.29E13 & 0 & 0 \\
    20 & \begin{tabular}[c]{c} \ce{Al2O3 -> Al2O3(B)} \\ STICK\end{tabular} & 1.00 & 0 & 0 \\
    21 & \ce{AlO2 + AlO(S) + Al(B) <-> Al2O3(B) + Al(L)} & 2.29E13 & 0 & 0 \\
    22 & \ce{AlO2(S) + AlO + Al(B) <-> Al2O3(B) + Al(L)} & 2.29E13 & 0 & 0 \\
    23 & \ce{Al2O2(S) + O + 3Al(B) <-> Al2O3(B) + 3Al(L)} & 2.29E13 & 0 & 0 \\
    24 & \ce{2AlO(S) + O(S) + 3Al(B) <-> Al2O3(B) + 3Al(L)} & 2.26E19 & 0 & 0 \\
    25 & \ce{3O(S) + 2Al(L) + 5Al(B) -> Al2O3(B) + 5Al(L)} & 1.00E48 & 0 & 0 \\ \bottomrule
    \end{tabular}
    \caption{Surface mechanism}
    \label{table:surf_mech}
\end{table}

The thermochemical properties for the surface mechanism are adopted from \citep{glorian2014cinetique}. The surface reactions can be represented in the general form as
\begin{equation}
\sum_{k=1}^{K_g+K_s}v'_{ki}A_k \Leftrightarrow \sum_{k=1}^{K_g+K_s}v'_{ki}A_k \quad (i=1,\cdots,N_s)
\end{equation}
The rate-of-progress variable for the $i$th surface reaction is given by
\begin{equation}
q_i = k_{f,i}\prod_{k=1}^{K_g+K_s}[X_k]^{v'_{ki}} - k_{r,i}\prod_{k=1}^{K_g+K_s}[X_k]^{v''_{ki}}
\end{equation}
where $[X_k]$ represents a volume (mol/m3) for gas species and a surface molar concentration (mol/m2) for surface species. Surface concentration reads
\begin{equation}
[X_k] = \frac{\theta_k \Gamma}{\sigma_k}
\end{equation}
where $\theta_k$ is the surface site fraction (coverage) for the $k$th species, $\Gamma$ is the total density of open sites ($\Gamma=4.42\times 10^9 \mathrm{mol/m^2}$ for a neat \ce{Al} (111) surface) and $\sigma_k$ is the number of sites occupied by the $k$th species. In surface kinetics, the forward rate $k_{f,i}$ can be given in STICK form with the Motz-Wise correction as
\begin{equation}
k_{f,i} = \frac{\gamma_i}{1-\gamma_i/2} \frac{\prod_{k=1}^{K_s}\sigma_k^{v'_{ki}}}{\Gamma_m} \sqrt{\frac{RT}{2\pi W_k}}
\end{equation}
where $m$ is the sum of all the stoichiometric coefficients of reactants that are surface species. For reversible reactions, the reverse rate constants $k_{r,i}$ are related to the forward rate through the equilibrium constant as $k_{r,i}=k_{f,i}/K_{c,i}$ where $K_{c,i}$ is the equilibrium constant given as
\begin{equation}
K_{c,i} = K_{p.i}\left(\frac{P_\mathrm{atm}}{RT}\right)^{\zeta_{ki}^g} \Gamma^{\zeta_{ki}^s} \prod_{k=1}^{K_s}\sigma_k^{-v_{ki}} \quad \zeta_{ki}^g=\sum_{k=1}^{K_g}v_{ki}, \zeta_{ki}^s=\sum_{k=1}^{K_s}v_{ki}
\end{equation}
The net mass surface production rate $\dot{\omega}_k^s$ for the $k$th species can be computed by
\begin{equation}
\dot{\omega}_k^s = W_k \sum_{i=1}^{N_s} v_{ki}q_i
\end{equation}
The concentration of surface species obeys
\begin{equation}
\frac{\mathrm{d}[X_k]}{\mathrm{d}t} = \frac{\dot{\omega}_k^s}{W_k}
\end{equation}

\subsection{Surface Global reaction} \label{sec:1.3}
Due to the complexity of the implementation of the surface mechanism, global mechanism is also suggested to model the mass and energy exchange between gas phase and condensed phase \citep{zhang2022detailed}. The change of droplet mass is attributed to the evaporation and global heterogeneous surface reaction (HSR) as
\begin{equation}
\dot{m}_p = -(\dot{m}_{p,\mathrm{evap}} + \dot{m}_{p,\mathrm{HSR}})
\label{equ:mp}
\end{equation}

For the evaporation model, it is expressed as the classical $d^n$ law as
\begin{equation}
\dot{m}_{p,\mathrm{evap}} = \pi(1-\beta)d_{p,\ce{Al}}\mathrm{Sh}\rho D_F \ln(1+B_M)
\end{equation}
where $D_F$ is the diffusion coefficient of the fuel vapor in the mixture and $B_M$ is the Spalding mass transfer number defined as
\begin{equation}
B_M = \frac{Y_{F,s} - Y_{F,\infty}}{1 - Y_{F,s}}
\end{equation}
where $Y_{F,s}$ and $Y_{F,1}$ are the mass fractions of the evaporated fuel at the droplet surface and in the far-field, respectively.

For the global HSR model, the global reaction reads
\begin{equation}
\ce{Al + 0.75 (O2 + 3.76 N2) <-> 0.5 Al2O3 + 2.82 N2}
\end{equation}
where the reaction rate is described by Arrhenius coefficients $A_r = 1.5\times 10^4$ m/s and $E_a = 83.72$ kJ/mol. The consumption rate of \ce{Al} in the HSR reads
\begin{equation}
\dot{m}_{p,\mathrm{HSR}}^{\ce{Al}} = A_{p,\mathrm{eff}}\rho_s Y_{\mathrm{ox},s} A_r\exp\left(-\frac{E_a}{RT_p}\right)
\end{equation}
where the effective particle surface $A_{p,\mathrm{eff}}$ is calculated from the diameter
\begin{equation}
d_{p,\ce{Al}} = \sqrt[3]{\frac{6m_{p,{\ce{Al}}}}{\pi\rho_{\ce{Al}}}}
\end{equation}
Considering the condensed phase product \ce{Al2O3}, the net mass rate of change from HSR reads
\begin{equation}
\dot{m}_{p,\mathrm{HSR}} = \dot{m}_{p,\mathrm{HSR}}^{\ce{Al}} - 0.5\frac{M_{\ce{Al2O3}}}{M_{\ce{Al}}}\dot{m}_{p,\mathrm{HSR}}^{\ce{Al}}
\end{equation}


%#############################################################
%#############################################################
\newpage
\section{Gas Phase Model}

\subsection{Oscillating flow velocity field}
For a microdroplet in an oscillating flow, Landau and Lifshitz \citep{landau1987fluid} derived the fluid velocity components on the droplet-centered coordination as
\begin{equation}
\left\{ \begin{aligned}
v_r &= -2u_0 e^{-i\omega t} F(r)\sin\theta \\
v_\theta &= u_0 e^{-i\omega t} G(r)\sin\theta \\
v_\psi &= 0
\end{aligned} \right.
\end{equation}
where $F(r)$ and $G(r)$ are damping factors in the radial and tangential directions, respectively, given by
\begin{equation}
\left\{ \begin{aligned}
F(r) &= \left[A e^{ikr} \left(r-\frac{1}{ik}\right) + B\right]\frac{1}{r^3} \\
G(r) &= \left[A e^{ikr} \left(ikr^2 - r+\frac{1}{ik}\right) - B\right]\frac{1}{r^3} \\
A &= -\frac{3a}{2ik}e^{ikr}, B=-\frac{a^3}{2}\left(1-\frac{3}{ika} -\frac{3}{k^2a^2}\right), k=\frac{1+i}{\sqrt{2\nu/\omega}}
\end{aligned} \right.
\end{equation}
where $v$ is the kinematic viscosity and $a$ is the droplet radius.

For the aluminum droplet combustion in the oscillating field, we consider that the influence of combustion on the flow velocity field can be neglected. So the velocity field can be regarded as a prior field which evoles with time as
\begin{equation}
\left\{ \begin{aligned}
v_r &= -2u_0 e^{-i\omega t} F(r)\sin\theta - \omega A\cos\omega t\cos\theta \\
v_\theta &= u_0 e^{-i\omega t} G(r)\sin\theta + \omega A\cos\omega t\sin\theta\\
v_\psi &= 0
\end{aligned} \right.
\end{equation}
where $A$ is the oscillatory amplitude of the field.

\subsection{Gas phase governing equations}
The governing equations for gas phase include continuity, species transport and energy equation as follows
\begin{equation}
\begin{aligned}
\frac{\partial}{\partial t}\rho_g + \nabla\cdot(\rho_g\mathbf{u}) &= 0 \\
\frac{\partial}{\partial t}(\rho_g Y_k) + \nabla\cdot(\rho_g\mathbf{u}Y_k) - \nabla\cdot\left(\rho_g D_k\nabla Y_k\right) &= \dot{\omega}_k^s \\
\frac{\partial}{\partial t}(\rho_g h_s) + \nabla\cdot(\rho_g\mathbf{u}h_s) - \nabla\cdot\left(\rho_g \alpha_g\nabla h_s\right) &= \rho_g\dot{Q}_g 
\end{aligned}
\label{equ:govern}
\end{equation}
where $D_k$ is the diffusion coefficient of species $k$, $\alpha_g$ is the thermal diffusion coefficient of the gas mixture, $h_s$ is the sensible enthalpy of the gas mixture, $\dot{\omega}_k^s$ and ̇$\dot{Q}_g$ depict the chemical reaction rate for species $k$ and heat release rate due to gas phase reactions, respectively. Notably, the momentum equation is omitted because the velocity field is imposed from the oscillation.

\subsection{Finite volume method (FVM)}
Under FVM method, the scalar field is centered on every cell of the computational domain and scalar flux is computed on the faces of every cell. The above governing equations can be generalized to advection-diffusion-reaction equation as
\begin{equation}
\frac{\partial}{\partial t}(\rho_g \phi) + \nabla\cdot(\rho_g\mathbf{u}\phi) - \nabla\cdot\left(\Gamma^\phi\nabla \phi\right) = Q^\phi
\end{equation}
where $\phi$ is the scalar and $\Gamma^\phi$ is the diffusivity of the scalar. Integrating over the volume of an element, we have
\begin{equation}
\int_\Omega \frac{\partial}{\partial t}(\rho_g \phi) \mathrm{d}\Omega + \int_\Omega \nabla\cdot(\rho_g\mathbf{u}\phi) \mathrm{d}\Omega - \int_\Omega \nabla\cdot\left(\Gamma^\phi\nabla \phi\right) \mathrm{d}\Omega = \int_\Omega Q^\phi \mathrm{d}\Omega
\end{equation}
Using the divergence theorem, the above can be re-written in terms of surface integrals as
\begin{equation}
\int_\Omega \frac{\partial}{\partial t}(\rho_g \phi) \mathrm{d}\Omega + \int_S (\rho_g\mathbf{u}\phi) \mathrm{d}S - \int_S \left(\Gamma^\phi\nabla \phi\right) \mathrm{d}S = \int_\Omega Q^\phi \mathrm{d}\Omega
\end{equation}
In the finite volume form, the equation reads
\begin{equation}
\int_\Omega \frac{\partial}{\partial t}(\rho_g \phi) \mathrm{d}\Omega + \sum_{i=1}^{n(\Omega)}\int_{S_i} (\rho_g\mathbf{u}\phi) \mathrm{d}{S_i} - \sum_{i=1}^{n(\Omega)}\int_{S_i} \left(\Gamma^\phi\nabla \phi\right) \mathrm{d}{S_i} = \int_\Omega Q^\phi \mathrm{d}\Omega
\end{equation}
By discretization, the convective and diffusive fluxes are computed at faces and time-variant and sources terms are computed at the cells.

\subsection{Boundary conditions}
The boundary conditions include the far-field boundary and the surface boundary. For the far-field boundary, the temperature, pressure and species mass fractions are set as constants
\begin{equation}
T = T_\infty, P = P_\infty, Y_k = Y_{k,\infty} \quad \forall k \in \{0, \cdots, N_s\}
\end{equation}

For the surface boundary, the mass and energy flux are balanced with surface reaction ({\color{red} The consumption of gas-phase aluminum is balanced by the evaporation from the surface (such that have mass source term), the consumption and creation of other species is balanced by the convection and diffusion (such that have no source term)}). In the surface model from surface mechanism in Section \hyperref[sec:1.2]{1.2}, the mass balance is reached between convective, diffusive fluxes and surface production mass rates as
\begin{equation}
\dot{m}_\mathrm{vap}Y_k^s - \rho D_k\left.\frac{\mathrm{d}Y_k}{\mathrm{d}n}\right|_s = \dot{\omega}_k^s
\end{equation}
where $\dot{\omega}_k^s$ is computed from surface mechanism. Compared with Equation (18) from \citep{gallier2023aluminum}, he made a mistake in the sign of the r.h.s. of the equation. The correction to plus sign has also been made in Equation (6) of \citep{qiu2024detailed}. Considering that the only vaporized species from surface is \ce{AL}, the above equation can be written as
\begin{equation}
\begin{aligned}
\dot{m}_\mathrm{vap}Y_{\ce{AL}}^s - \rho D_{\ce{AL}}\left.\frac{\mathrm{d}Y_{\ce{AL}}}{\mathrm{d}n}\right|_s &= \dot{m}_\mathrm{vap} \\
\dot{m}_\mathrm{vap}Y_k^s - \rho D_k\left.\frac{\mathrm{d}Y_k}{\mathrm{d}n}\right|_s &= 0, \quad k\neq \ce{AL}
\end{aligned}
\end{equation}
where $K_s$ equations are used to solve $K_s+1$ scalars including $Y_k$ and $\dot{m}_\mathrm{vap}$. The left closure equation is from surface reaction kinetics as $\dot{m}_\mathrm{vap} = f(Y_k)$. Here we use the Hertz-Knudsen relation as \citep{gallier2023aluminum} where
\begin{equation}
\dot{m}_\mathrm{vap} = \alpha \sqrt{\frac{M}{2\pi RT_\mathrm{sat}}} (P^* - P_\mathrm{sat})
\end{equation}
where $\mathrm{sat}$ denotes saturation quantity.

The energy balance equation at the surface reads
\begin{equation}
-\lambda \left.\frac{\mathrm{d}T}{\mathrm{d}n}\right|_s = \dot{m}_\mathrm{vap}(h_{\ce{AL(L)}} - h_{\ce{AL}})
\end{equation}
where the temperature at the surface $T_s$ can be solved.

% In the surface model from surface global reaction in Section \hyperref[sec:1.3]{1.3}, the mass flux is attributed to both evaporation and HSR as shown in Equation \hyperref[equ:mp]{1.8} which can be added to the source term of the transport equation of gas phase in Equation \hyperref[equ:transport]{2.5}. As for the energy flux, three physical processes including interphase heat transfer, evaporation and HSR are considered such that the energy source term can be added to the energy equation of gas phase in Equation \hyperref[equ:energy]{2.4} reads
% \begin{equation}
% \begin{aligned}
% \dot{S}_p &= -\dot{q}_{p,\mathrm{inter}} + \dot{m}_{p,\mathrm{evap}} h_{g,\mathrm{evap}} 
% + \dot{m}_{p,\mathrm{HSR}} h_{g,\mathrm{evap}} \\
% \dot{q}_{p,\mathrm{inter}} &= k_g (T_g - T_p) \mathrm{Nu}_p \\
% h_{g,\mathrm{evap}} &= -\theta_\mathrm{st} h_{\ce{O2}}(T_g)
% \end{aligned}
% \end{equation}
% where $k_g$ is the thermal conductivity, $\theta_\mathrm{st}$ is the stoichiometric oxygen mass fraction required for HSR.

\subsection{Example: surface evaporation of oscillating flow field}
As an example, the governing equation for a pure evaporation droplet reduces to
\begin{equation}
\frac{\partial C_i}{\partial t} + \mathbf{v}\cdot\nabla C_i = D_{ij}\nabla^2 C_i
\end{equation}
where $C_i$ is the vapor concentration and is set to be constant as $C_s$ at the surface of the droplet. It is convenient to write the governing equation in dimensionless form by introducing the dimensionless quantities
\begin{equation}
C_i^* = \frac{C_i}{C_s}, v_r^* = \frac{v_r}{u_0}, v_\theta^* = \frac{v_\theta}{u_0}, r^* = \frac{r}{a}, \theta^* = \frac{\theta}{\pi}, t^* = \omega t
\end{equation}
The dimensionless governing equation reads
\begin{equation}
\begin{aligned}
\mathrm{St}\frac{\partial C_i^*}{\partial t^*} &+ (v_r^* - \cos t^*\cos\pi\theta^*) \frac{\partial C_i^*}{\partial r^*} 
+ \frac{v_\theta^* - \cos t^*\sin\pi\theta^*}{r^*\pi} \frac{\partial C_i^*}{\partial \theta^*} 
= \frac{2}{\mathrm{Pe}} \left(\frac{\partial^2 C_i^*}{\partial {r^*}^2} + \frac{2}{r^*}\frac{\partial C_i^*}{\partial r^*}\right)    
\end{aligned}
\end{equation}
where $\mathrm{St} = xa/u_0$ is the Strouhal number, and $\mathrm{Pe} =2au_0/ D_{ij}$ is the Peclet number.


%#############################################################
%#############################################################
\newpage
\section{Numerical Resolution of the 1D Model Equations}

This is an English Translation of the Section 1.2 of the Thesis \textit{Modélisation de la combustion de gouttes d'aluminium dans les conditions d'un moteur fusée à propergol solide} (Modeling the combustion of aluminum droplets under the conditions of a solid propellant rocket engine) by \textbf{Mathieu Muller} at ONERA.

\subsection{Finite volume method}

Solving the system of partial differential equations (\hyperref[equ:pde]{3.1}) requires time and space discretization. Different discretization methods are possible. In computational fluid mechanics, the most widely used method is the finite volume method. This method is constructed from an integral formulation based on the conservative form of the equations to be solved. The integrals do not apply over the entire domain but over a set of cells called control volumes separated by their interfaces. With this method, the divergence terms appearing in the equations to be solved are treated using the Green-Ostrogradski theorem: the volume integrals of a divergence term are transformed into flow surface integrals. The flows at the interfaces are evaluated using a numerical scheme. The finite volume method has the advantage of being conservative because the flow entering a control volume is equal to the flow leaving the adjacent volume in the case of a stationary problem. It is therefore particularly suitable for solving conservation equations. In our one-dimensional approach, each control volume is represented by a spherical layer.

\begin{equation} 
\left\{ \begin{aligned} 
\frac{\partial (\rho A h)}{\partial t} + \nabla (Mh) + \nabla (Aq) &= 0 \\
\frac{\partial (\rho A Y_k)}{\partial t} + \nabla (M Y_k) + \nabla (Aq) &= A \dot{m}_k \\
\frac{\partial (\rho A)}{\partial t} + \nabla M &= 0 \\
\end{aligned} \right. 
\label{equ:pde}
\end{equation}

\subsection{Algorithmic procedure}

The diagram in Figure 1.8 represents the algorithmic structure developed to simulate the combustion of an isolated \ce{Al} droplet in unsteady approach.

This procedure is composed of two blocks. The first concerns the initialization for the calculation. Specific procedures are used to read the data related to reactions and chemical species (thermodynamic and transport data). The options for the transport model and the numerical methods that we wish to use to perform a simulation are entered in an input file and read by the procedure before starting the calculation. It is also necessary to define the initial state for the gas phase (mesh, temperature profile and chemical species), for the drop (temperature and radius), and for the surface (fractions of surface species). This information can be directly entered in input files or retrieved from a previous calculation: the initial state for the new calculation then corresponds to the final state obtained in a previous simulation. This state can be projected by interpolation onto a different mesh.

The second block of this procedure is dedicated to solving the equations for the gas phase, the droplet and its surface. The resolution methods used are implicit to obtain better numerical stability. The implemented method allows calculating the off-surface gas state, the surface gas state and the surface state. The details of the time integration are described below.

\subsection{Time integration}

\subsubsection{Calculation of the time step}
The discretization of the transport equations reveals the time step $\Delta t$ that must be evaluated. For a given spatial discretization, a time step that is too large implies a significant integration error. On the contrary, a time step that is too small requires an unnecessary number of iterations to reach a given physical time. To calculate the time step, the Courant-Friedrichs-Lewy condition is used. The Courant CFL number associated with this condition for a one-dimensional scheme is defined by:
\begin{equation}
\mathrm{CFL} = \frac{u \Delta t}{\Delta x}
\end{equation}
where $\Delta x$ is the spatial discretization step, $u$ the velocity. To start a calculation, the initial time step is imposed. A small value of the order of $10^{-10}$ s may be necessary in some cases to start the integration. The time step is then gradually increased during the time advance until $\mathrm{CFL} = 1$ is obtained in order to minimize the calculation time by ensuring a correct treatment of the convective transport for an unsteady problem. The CFL current number can be greater than 1 if we only want to obtain an established solution. The time step based on the CFL is calculated by:
\begin{equation}
\Delta t_\mathrm{CFL} = \mathrm{CFL} \min_j^{N_r} \left(
\frac{\Delta r_j}{\max_j^{N_r} \left(
\left|u_{j-\frac{1}{2}}\right|, \left|u_{j}\right|, \left|u_{j+\frac{1}{2}}\right|, \varepsilon
\right)}\right)
\end{equation}
where $j$ is the cell number, Nr is the number of cells, $\left|u_{j-\frac{1}{2}}\right|$, $\left|u_{j}\right|$ and $\left|u_{j+\frac{1}{2}}\right|$ are respectively the absolute velocities calculated at the upstream, center and downstream interfaces of cell $i$, $\varepsilon$ is a lower limit introduced to avoid an infinite time step if the gas is initially at rest, $\Delta r_j$ is the radial size of cell $i$ and $\mathrm{CFL}$ is the imposed Current number.

\subsubsection{Calculation of the state of the gas phase}
The computational domain, between the droplet surface and the outer boundary, is divided into cells for which the energy and mass balances of the species are established (see Figure 1.9). The interfaces of each cell correspond to concentric spheres. The gas state is evaluated at the center of a cell $j$ while the fluxes are determined at the interfaces $j \pm 1/2$, the numerical scheme is thus conservative. An additional computation point is defined corresponding to the droplet surface.

Since the numerical error of the solution depends on the size of the cells in the computational domain, it is necessary to provide a means to control this error. Using a uniform grid with a pitch adapted to the area requiring the finest resolution is not a good strategy because it is very expensive. The initial grid must be fine enough to perform the calculation with the unsteady approach, particularly in the area close to the droplet. The initial grid is defined by the coordinates $r_j$ and the adaptation is parameterized in terms of the local gradient and curvature of the solution $(T, Y_k)_j$. 

The discretization of equations (\hyperref[equ:pde]{3.1}) can be carried out using several methods. The balance equations at time $n + 1$ for cell $j \in [1;N_r]$ are as follows:
\begin{equation} 
\left\{ \begin{aligned}
\frac{\rho_j^{n+1} V_j^{n+1} h_j^{n+1} - \rho_j^{n} V_j^{n} h_j^{n}}{\Delta t^n} + \theta \Delta(AQ)_j^{n+1} + (1-\theta) \Delta (AQ)_j^n &= 0 \\
\frac{\rho_j^{n+1} V_j^{n+1} Y_{k,j}^{n+1} - \rho_j^{n} V_j^{n} Y_{k,j}^{n}}{\Delta t^n} + \theta \Delta(AJ)_j^{n+1} + (1-\theta) \Delta (AJ)_j^n 
&= \theta V_j^{n+1} \dot{m}_{k,j}^{n+1} + (1-\theta) V_j^{n} \dot{m}_{k,j}^{n} \\
\frac{\rho_j^{n+1} V_j^{n+1} - \rho_j^{n} V_j^{n}}{\Delta t^n} + \theta \Delta(M)_j^{n+1} + (1-\theta) \Delta (M)_j^n &= 0 \\
\end{aligned} \right.
\label{equ:discrete}
\end{equation}
where $V_j$ is the volume of cell $j$ and the parameter of the integration scheme. The discretization is carried out according to the implicit Euler method if $\theta = 1$ and according to the Crank-Nicolson method if $\theta = 0.5$. The differences of the fluxes $\Delta (AQ)^n_j$, $\Delta (AJ)^n_j$ and $\Delta (M)^n_j$ obtained at time $n$ between the interfaces $j+\frac{1}{2}$ and $j-\frac{1}{2}$ are calculated by
\begin{equation}
\left\{ \begin{aligned}
\Delta (AQ)_j^n 
&= \left(M_{j+\frac{1}{2}}^n h_{j+\frac{1}{2}}^n - M_{j-\frac{1}{2}}^n h_{j-\frac{1}{2}}^n\right) + 
\left(A_{j+\frac{1}{2}}^n q_{j+\frac{1}{2}}^n - A_{j-\frac{1}{2}}^n q_{j-\frac{1}{2}}^n\right) \\
\Delta (AJ)_j^n 
&= \left(M_{j+\frac{1}{2}}^n Y_{k,j+\frac{1}{2}}^n - M_{j-\frac{1}{2}}^n Y_{k,j-\frac{1}{2}}^n\right) + 
\left(A_{j+\frac{1}{2}}^n J_{k,j+\frac{1}{2}}^n - A_{j-\frac{1}{2}}^n J_{k,j-\frac{1}{2}}^n\right) \\
\Delta (M)_j^n 
&= M_{j+\frac{1}{2}}^n - M_{j-\frac{1}{2}}^n \\
\end{aligned} \right.
\end{equation}
where $M_{j\pm\frac{1}{2}}$, $h_{j\pm\frac{1}{2}}$, $A_{j\pm\frac{1}{2}}$, $q_{j\pm\frac{1}{2}}$, $Y_{k,j\pm\frac{1}{2}}$ and $J_{k,j\pm\frac{1}{2}}$ are respectively the flow rate, mass enthalpy, surface area, heat flux, mass fraction of species $k$ and diffusive mass flux of species $k$ applied to the interfaces $j\pm\frac{1}{2}$.

For the $N_r+1$ cell, the variables $T_{N_r+1}$ and $Y_{k,N_r+1}$ can be directly imposed: this allows exchanges with the surrounding environment. It is also possible to impose a zero flow rate and symmetry conditions on the external boundary (equality of the $N_r$ and $N_r+1$ states): the mass and thermal fluxes at the $N_r+\frac{1}{2}$ interface are then zero, which ensures conservation of mass and energy in the domain including the drop.

The heating of the gas during combustion causes it to expand: the external boundary moves in order to maintain a constant pressure in the gas. For the $N_r$ cell, we then modify the mass balance equation by replacing the conservation of mass in the cell with the conservation of mass over the entire domain:
\begin{equation}
\frac{m_t^{n+1} - m_t^n}{\Delta t^n} - M_0^n = 0
\end{equation}
where $M^n_0$ is the mass flow rate at the droplet surface and $m_t$ is the total mass of gases in the domain, defined by
\begin{equation}
m_t = \sum_{j=1}^{N_r} \rho_j V_j
\end{equation}

Thermodynamic and molecular transport properties are determined at the cell centers. Their evaluation at the interfaces depends on the numerical scheme used for each flow. Generally, the value of a physical quantity $Q$ at the interface $j>0$ is calculated as follows
\begin{equation}
Q_{j+\frac{1}{2}} = \phi_{j+\frac{1}{2}}^- Q_j + \phi_{j+\frac{1}{2}}^+ Q_{j+1}
\end{equation}
where $\phi^-_{j+\frac{1}{2}}$ and $\phi^+_{j+\frac{1}{2}}$ are the coefficients of the scheme. The fluxes are then assembled from the quantities at the corresponding interfaces. For molecular transport fluxes, the numerical scheme is centered of order 2 with $\phi^+_{j+\frac{1}{2}} = 0.5$. For convective fluxes, the scheme is decentered upstream to ensure the stability of the numerical solution. The decentering of the scheme is variable depending on the local Péclet number:
\begin{equation}
\mathrm{Pe}_{j+\frac{1}{2}} = \frac{1}{2} \left(\mathrm{Pe}_{1,j} + \mathrm{Pe}_{1,j+1}\right) \left(r_{j+1} - r_j\right), \quad \mathrm{Pe}_1 = \frac{c_p M}{A \lambda}
\end{equation}
where $\mathrm{Pe}_1$ is the Peclet number for a unit length. The coefficients of the off-center scheme are calculated as follows, with the indices omitted:
\begin{equation}
\phi^\pm = \frac{\hat{\mathrm{Pe}} \pm \mathrm{Pe}}{2\hat{\mathrm{Pe}}}, 
\hat{\mathrm{Pe}} = \left\{ \begin{aligned}
|\mathrm{Pe}|, \quad |\mathrm{Pe}| > \mathrm{Pe}_\mathrm{lim} \\
\frac{\mathrm{Pe}^2 + \mathrm{Pe}_\mathrm{lim}^2}{2\mathrm{Pe}_\mathrm{lim}}, \quad |\mathrm{Pe}| < \mathrm{Pe}_\mathrm{lim} \\
\end{aligned} \right.
\end{equation}
The $\mathrm{Pe}_\mathrm{lim}$ parameter is taken equal to 1. When $\mathrm{Pe}_{j+\frac{1}{2}} > \mathrm{Pe}_\mathrm{lim}$, $\phi^-_{j+\frac{1}{2}}= 0$ and $\phi^+_{j+\frac{1}{2}} = 0$, which corresponds to the order 1 scheme decentered one upstream. When $\mathrm{Pe}_{j+\frac{1}{2}}$ tends to 0, the coefficients of the scheme tend to 0.5, so the scheme becomes centered of order 2. This principle makes it possible to improve the accuracy of the scheme, when the cells are sufficiently small and viscous transport is predominant compared to convective transport, while preserving the stability of the numerical solution. The gradients of the physical quantities in the molecular transport flows are approximated by the centered finite differences:
\begin{equation}
\nabla Q_{j+\frac{1}{2}} = \frac{Q_{j+1} - Q_j}{r_{j+1} - r_j}
\end{equation}

The discretized transport equations represent a system of coupled nonlinear equations, which is solved by the Newton-Raphson method. Residuals are constructed from the discretized equations (\hyperref[equ:discrete]{3.4}):
\begin{equation}
\left\{ \begin{aligned}
r_{j,T}^{n+1} =& \frac{\rho_j^{n+1} V_j^{n+1} h_j^{n+1} - \rho_j^{n} V_j^{n} h_j^{n}}{\Delta t^n} &+ \theta \Delta(AQ)_j^{n+1} &+ (1-\theta) \Delta (AQ)_j^n \\
r_{j,k}^{n+1} =& \frac{\rho_j^{n+1} V_j^{n+1} Y_{k,j}^{n+1} - \rho_j^{n} V_j^{n} Y_{k,j}^{n}}{\Delta t^n} &+ \theta \Delta(AJ)_j^{n+1} &+ (1-\theta) \Delta (AJ)_j^n \\
& &- \theta V_j^{n+1} \dot{m}_{k,j}^{n+1} &- (1-\theta) V_j^{n} \dot{m}_{k,j}^{n} \\
r_{j,m}^{n+1} =& \frac{\rho_j^{n+1} V_j^{n+1} - \rho_j^{n} V_j^{n}}{\Delta t^n} &+ \theta \Delta(M)_j^{n+1} &+ (1-\theta) \Delta (M)_j^n \\
\end{aligned} \right.
\end{equation}
where $r_{j,T}^{n+1}$ , $r_{j,k}^{n+1}$ and $r_{j,m}^{n+1}$ are respectively the residues of the conservation equations of energy, mass of species $k$ and total mass in cell $j$ at time $n+1$. The vector $\mathbf{u}^n_j$ of unknowns in cell $j$ and the vector $\mathbf{U}^{n+1}$ of all unknowns at time $n+1$ are defined by:
\begin{equation}
\mathbf{u}_j^{n+1} = \begin{pmatrix}
T_j^{n+1} \\ Y_{j,1}^{n+1} \\ \vdots \\ Y_{j,N_g}^{n+1} \\ m_j^{n+1}
\end{pmatrix}, \quad \mathbf{U}^{n+1} = \begin{pmatrix}
\mathbf{u}_1^{n+1} \\ \vdots \\ \mathbf{u}_j^{n+1} \\ \vdots \\\mathbf{u}_{N_r}^{n+1}
\end{pmatrix}
\end{equation}
where $N_g$ is the number of species in the gas phase. The vector $\mathbf{r}^{n+1}_j$ of residues in cell $j$ and the vector $\mathbf{R}^{n+1}$ of all residues at time $n$ are defined by:
\begin{equation}
\mathbf{r}_j^{n+1} = \begin{pmatrix}
r_{j,T}^{n+1} \\ r_{j,1}^{n+1} \\ \vdots \\ r_{j,N_g}^{n+1} \\ r_{j,m}^{n+1}
\end{pmatrix}, \quad \mathbf{R}^{n+1} = \begin{pmatrix}
\mathbf{r}_1^{n+1} \\ \vdots \\ \mathbf{r}_j^{n+1} \\ \vdots \\\mathbf{r}_{N_r}^{n+1}
\end{pmatrix}
\end{equation}

The principle of the Newton-Raphson method is to locally linearize the system of discretized equations and then perform an iteration aiming for zero residues. The iterative process stops when the norm of the residues is less than an imposed tolerance or the change in the solution between two iterations becomes small.

The Jacobian matrix of the system is defined by:
\begin{equation}
\mathbb{J}^{n+1} = \frac{\partial \mathbf{R}^{n+1}}{\partial \mathbf{U}^{n+1}}
\end{equation}
This matrix is block tridiagonal because the fluxes at the interfaces $j\pm\frac{1}{2}$ are calculated from the states of cells $j-1$, $j$, and $j+1$. Usually, this matrix is obtained approximately by perturbing each component of the vector $\mathbf{U}_{n+1}$ and then calculating all the elements of the vector $R_{n+1}$. Since the matrix is block tridiagonal, it is possible to perturb the state in every third cell at the same time. The iterative cycle of the Newton-Raphson method begins by defining a first approximation for the vector of unknowns $\mathbf{U}_l^{n+1}$ with $l=1$ for the iteration number. We then calculate the residuals $\mathbf{R}n+1 l = R Un+1 l$ and the Jacobian matrix $\mathbb{J}^{n+1}_l = \mathbb{J}\left(\mathbf{U}^{n+1}_l\right)$. The approximate solution is corrected by:
\begin{equation}
\mathbf{U}^{n+1}_{l+1} = \mathbf{U}^{n+1}_l - F_u \left(\mathbb{J}^{n+1}_l\right)^{-1} \mathbf{R}^{n+1}_l
\end{equation}
where $F_u$ is a damping factor introduced to ensure that the norm $\Vert \mathbf{R}^{n+1}_{l+1} \Vert$ is less than the norm $\Vert \mathbf{R}^{n+1}_{l} \Vert$. The inversion of the Jacobian matrix is facilitated by LU factorization.

\subsubsection{Calculation of surface condition}
The discretization of the transport equations in the gas reveals the fluxes at the interfaces, including those at the surface of the droplet which depend on the gas state and the surface species. The objective is to determine the state at the surface by verifying the balances of mass fluxes and thermal fluxes. To do this, we calculate the residuals associated with equations (1.1.51):
\begin{equation}
\mathbf{r}_s^n = \begin{pmatrix}
r_k^n \\ r_T^n \\
\end{pmatrix} = \begin{pmatrix}
\rho Y_k (u + v_k) |_{j=0} - \dot{s}_k \\ T_p - T_g|_{j=0}
\end{pmatrix}
\end{equation}
To solve equations (1.1.51), we use Newton's method according to which the system is represented in the following linearized form:
\begin{equation}
\mathbb{J}_s^n \delta\mathbf{u}^n = -\mathbf{r}^n
\end{equation}
where $\mathbb{J}^n_s$ is the Jacobian matrix $(K_g+1)\times(K_g+1)$ defined as follows:
\begin{equation}
\mathbb{J}^n_s = \frac{\partial \mathbf{r}^n}{\partial \mathbf{u}^n}
\end{equation}
and $\mathbf{u}^n$ is the vector of unknowns given by:
\begin{equation}
\mathbf{u}^{n} = \begin{pmatrix}
Y_{k_g}^{n} \\ \vdots \\ Y_{K_g}^{n} \\ T^{n}
\end{pmatrix}
\end{equation}
The elements $J_{s,kl}$ of the Jacobian matrix $\mathbb{J}^n_s$ are calculated numerically by perturbing each component of the vector $\mathbf{u}^n$ and recalculating $\mathbf{r}^n$ following each perturbation. Each time a component of the residual vector is modified, the gas properties, the diffusion fluxes of the $J_k$ species and the fluxes due to surface reactions are calculated by a method described in section 1.2.3.3.2. A Newtonian iteration is formally described by two steps:
\begin{equation}
\begin{aligned}
\delta \mathbf{u}_l^{n+1} &= -\left(\mathbf{J}_{sl}^{n+1}\right)^{-1} \mathbf{r}_l^n \\
\mathbf{u}_{l+1}^{n+1} &= \mathbf{u}_{l}^{n+1} + F \delta \mathbf{u}_{l}^{n}
\end{aligned}
\end{equation}
where $F\le 1$ is the damping factor. This term is introduced to ensure that the norm $\Vert \mathbf{r}^{n+1}_{l+1} \Vert$ is less than $\Vert \mathbf{r}^{n+1}_{l} \Vert$. The iterative process of Newton's method stops when the components of the vector of corrections of $\mathbf{u}^{n+1}_l$ become small.

For each modification of the vector $\mathbf{u}^{n+1}_{j=0}$ , the fluxes due to surface reactions are recalculated. To do this, it is necessary to calculate the mass fractions of the surface species by solving equation (1.1.48) for the new temperature and composition of the gaseous species at the surface. Several numerical methods can be used to solve this equation. The first method is that of trapezoids. By this method, the discretization of equation (1.1.48) gives:
\begin{equation}
\frac{\gamma_k^{n+1} - \gamma_k^{n}}{\Delta t^n} = \frac{1}{2} \left(
    \dot{s}_k^{n+1}\sigma_k^{n+1} + \dot{s}_k^{n}\sigma_k^{n}
\right)
\end{equation}
In vector form, it becomes:
\begin{equation}
\frac{\gamma^{n+1} - \gamma^{n}}{\Delta t^n} = \frac{1}{2} \left(
    \dot{\gamma}^{n+1} + \dot{\gamma}^{n}
\right)
\end{equation}
The Newton-Raphson method is used to find the state $\gamma^{n+1}$. The system of equations (1.2.22) is represented in the following linearized form:
\begin{equation}
\mathbb{A}^{n+1} \delta\gamma^{n+1} = -\mathbf{r}^{n+1}
\end{equation}
where $\mathbf{r}^{n+1}$ is the vector of residues at time $n + 1$:
\begin{equation}
\mathbf{r}^{n+1} = \frac{\gamma^{n+1} - \gamma^{n}}{\Delta t^n} - \frac{1}{2} \left(
    \dot{\gamma}^{n+1} + \dot{\gamma}^{n}
\right)
\end{equation}
Omitting the exponent $n + 1$, we define the matrix $\mathbb{A}$:
\begin{equation}
\mathbb{A} = \frac{\partial\mathbf{r}}{\partial\gamma} = \frac{1}{\Delta t} \mathbb{I} - \frac{1}{2} \frac{\partial\dot{\gamma}}{\partial\gamma} = \frac{1}{\Delta t} \mathbb{I} - \frac{1}{2} \mathbb{J}
\end{equation}
where $\mathbb{I}$ is the identity matrix. The Jacobian matrix $\mathbb{J}^{n+1}$ is computed numerically by perturbing the components of the vector $\gamma^{n+1}$ and recalculating $\dot{\gamma}^{n+1}$ after each perturbation. The Newton-Raphson iteration over a time step is organized as follows. We define a first approach $\gamma^{n+1}_l = \gamma^n$ with $l = 1$ the iteration number. We then compute $\mathbf{r}^{n+1}_l = \mathbf{r}\left(\gamma^{n+1}_l\right)$ and $\mathbb{A}^{n+1}_l = \mathbb{A}\left(\gamma^{n+1}_l\right)$.

At the next iteration, the vector $\gamma^{n+1}_{l+1}$ is:
\begin{equation}
\gamma^{n+1}_{l+1} = \gamma^{n+1}_{l} + \delta \gamma^{n+1}_{l} 
= \gamma^{n+1}_{l} + F_\gamma \left(\mathbb{A}_l^{n+1}\right)^{-1} \mathbf{r}_l^{n+1}
\end{equation}
where $\gamma^{n+1}_l$ is the vector of site density corrections and $F_\gamma$ is a damping factor introduced to ensure that the norm $\Vert \mathbf{r}^{n+1}_{l+1} \Vert$ is less than the norm $\Vert \mathbf{r}^{n+1}_{l} \Vert$. Newton's iterative process stops when the following condition is satisfied:
\begin{equation}
\left|\delta \gamma_{k,l}^{n+1}\right| < \varepsilon_\gamma
\end{equation}
where is $\varepsilon_\gamma$ an imposed tolerance. 

Other methods for discretizing equation (1.1.48) are the ASIRK (Additive Semi-Implicit Runge-Kutta) methods presented in [Zhong, 1996].





%#############################################################
%#############################################################
\newpage
\section{Droplet lifetime oscillation driven instability}

\nomenclature[1]{$\bar{x}$}{average value}
\nomenclature[2]{$x'$}{perturbation value in real space}
\nomenclature[3]{$\hat{x}$}{perturbation value in Fourier space}
\nomenclature[4]{$u$}{axial velocity}
\nomenclature[5]{$v$}{radial velocity}
\printnomenclature

Theories and results following \citep{GENOT20195359}. 

NOTE: in sec:1 of \citep{GENOT20195359}, it was said that "\textit{A \textbf{reduction of the mean droplet lifetime} may also be observed in numerical simulations at \textbf{high acoustic levels}, but the following study is \textbf{limited to the low-amplitude acoustic response} in which case the mean droplet lifetime remains invariant.}" So later works can focus on the mechanism of mean droplet lifetime reduction driven by high-amplitude oscillation.

\subsection{Droplet lifetime model}

Traditionally, aluminum (Al) droplet combustion is modeled by the $D^2$ law
\begin{equation}
    \frac{dD^2}{dt} = -\frac{4\mu(1+B)\mathrm{Sh}}{\rho_p\mathrm{Pr}}
    \label{equ:1}
\end{equation}
the lifetime of the burning droplet is given by the integration
\begin{equation}
    t_c = \int dt = \int_{D_i}^{D_r} -\frac{\rho_p\mathrm{Pr}}{4\mu(1+B)\mathrm{Sh}}dD^2 = \frac{\rho_p\mathrm{Pr}\left(D_i^2-D_r^2\right)}{4\mu(1+B)\mathrm{Sh}}
\end{equation}
where $D_i$ and $D_r$ are the \textbf{initial} and \textbf{residual} diameters of the droplet. The linear model for lifetime fluctuation of droplet in \textbf{real} space reads ($D_r$ is fixed)
\begin{equation}
\begin{aligned}
    t_c' &= t_c - \bar{t_c} \\
    &= \frac{\rho_p\mathrm{Pr}}{4\mu(1+B)\mathrm{Sh}} \left\{[(\bar{D_i}+D_i')^2-D_r^2] - (\bar{D_i}^2-D_r^2)\right\} \\
    &\approx \frac{\rho_p\mathrm{Pr}}{4\mu(1+B)\mathrm{Sh}} 2\bar{D_i}D_i' \\
    &= \frac{2\bar{t_c}\bar{D_i}}{\bar{D_i}^2-D_r^2} D_i'
\end{aligned}
\end{equation}
in the \textbf{Fourier} space reads
\begin{equation}
    \hat{t_c} = \frac{2\bar{t_c}\bar{D_i}}{\bar{D_i}^2-D_r^2} \hat{D_i}
\end{equation}
which is the \textcolor{blue}{same} as equation (6) in \citep{GENOT20195359}

\subsection{Droplet axial velocity model}

Momentum equation for the droplet in axial direction reads
\begin{equation}
    \frac{\partial u_p}{\partial t} + u_p\frac{\partial u_p}{\partial x} + v_p\frac{\partial u_p}{\partial r} = -\frac{\delta u_p}{\tau_d}
\end{equation}
where $\delta u_p = u_p-u$ and $\tau_d$ is the droplet relaxation time given by
\begin{equation}
    \tau_d = \frac{1+B}{1+0.15\mathrm{Re}_p^{0.687}}\frac{\rho_p D^2}{18\mu}
\end{equation}
assume that the axial velocity field of the particle is uniform (\textcolor{red}{DO NOT work for transverse acoustic field}), we have
\begin{equation}
    \frac{\partial u_p}{\partial t} = -\frac{\delta u_p}{\tau_d}
\end{equation}
applying the linear perturbation theory, we have
\begin{equation}
    \frac{\partial \bar{u_p}}{\partial t} + \frac{\partial u_p'}{\partial t} = -\frac{\bar{\delta u_p} + \delta u_p'}{\bar{\tau_d} + \tau_d'} \approx -\left(\bar{\delta u_p} + \delta u_p'\right) \left(\frac{1}{\bar{\tau_d}} - \frac{\tau_d'}{\bar{\tau_d}^2}\right)
\end{equation}
eliminating the average and infinitesimal terms, we have 
\begin{equation}
    \frac{\partial u_p'}{\partial t} = -\frac{\delta u_p'}{\bar{\tau_d}} + \frac{\bar{\delta u_p}\tau_d'}{\bar{\tau_d}^2}
    \label{equ:9}
\end{equation}
take a closer look at the relaxation time perturbation
\begin{equation}
    \bar{\tau_d} + \tau_d' = \frac{(1+B)\rho_p}{18\mu}\left(\bar{D}^2+2\bar{D}D'\right) \left[\frac{1}{1+0.15\bar{\mathrm{Re}_p}^{0.687}} - \frac{0.15\times 0.687\bar{\mathrm{Re}_p}^{0.687}}{\left(1+0.15\bar{\mathrm{Re}_p}^{0.687}\right)^2}\frac{\mathrm{Re}_p'}{\bar{\mathrm{Re}_p}}\right]
\end{equation}
\begin{equation}
    C_{\mathrm{Re}} \coloneqq \frac{0.15\times 0.687\bar{\mathrm{Re}_p}^{0.687}}{1+0.15\bar{\mathrm{Re}_p}^{0.687}}
\end{equation}
for the perturbation of particle Reynolds number, we have
\begin{equation}
    \frac{\mathrm{Re}_p'}{\bar{\mathrm{Re}_p}} = \frac{D'}{\bar{D}} + \frac{\bar{\delta u_p}\delta u_p'}{|\mathbf{\bar{\delta u_p}}|^2}
\end{equation}
\begin{equation}
    \tau_d' = 2\bar{\tau_d}\frac{D'}{\bar{D}} - \bar{\tau_d}C_{\mathrm{Re}}\left(\frac{D'}{\bar{D}} + \frac{\bar{\delta u_p}\delta u_p'}{|\mathbf{\bar{\delta u_p}}|^2}\right)
    \label{equ:13}
\end{equation}
substituting \hyperref[equ:13]{(13)} into \hyperref[equ:9]{(9)}, we have
\begin{equation}
    \frac{\partial u_p'}{\partial t} = -\frac{u_p' - u'}{\bar{\tau_d}} + \frac{\bar{\delta u_p}}{\bar{\tau_d}}\left(2\frac{D'}{\bar{D}} - C_{\mathrm{Re}}\frac{D'}{\bar{D}} - C_{\mathrm{Re}}\frac{\bar{\delta u_p}(u_p' - u'))}{|\mathbf{\bar{\delta u_p}}|^2}\right)
\end{equation}
converting to the Fourier space, we have
\begin{equation}
    i\omega\bar{\tau_d}\hat{u_p} + \hat{u_p} = \hat{u} + (2-C_{\mathrm{Re}})\bar{\delta u_p}\frac{\hat{D}}{\bar{D}} - \frac{C_{\mathrm{Re}}\bar{\delta u_p}^2}{|\mathbf{\bar{\delta u_p}}|^2} (\hat{u_p} - \hat{u})
    \label{equ:15}
\end{equation}
where the Fourier formula is used
\begin{equation}
    \mathcal{F}\left(\frac{\partial x}{\partial t}\right) = i\omega\mathcal{F}(x)
\end{equation}
rearranging \hyperref[equ:15]{(15)}, we have
\begin{equation}
    \hat{u_p}\underbrace{\left(i\omega\bar{\tau_d} + 1 + \frac{C_{\mathrm{Re}}\bar{\delta u_p}^2}{|\mathbf{\bar{\delta u_p}}|^2}\right)}_{\coloneqq C_1} = \hat{u} \underbrace{\left(1 + \frac{C_{\mathrm{Re}}\bar{\delta u_p}^2}{|\mathbf{\bar{\delta u_p}}|^2}\right)}_{\coloneqq C_2} + \frac{\hat{D}}{\bar{D}}\underbrace{(2-C_{\mathrm{Re}})}_{\coloneqq C_3}\bar{\delta u_p}
    \label{equ:17}
\end{equation}
which is the \textcolor{blue}{same} as equation (13) in \citep{GENOT20195359}

\subsection{Droplet diameter model}

We have droplet combustion in the Lagrangian framework as \hyperref[equ:1]{(1)} (where the target is on the droplet). In the Eulerian framework (where the target is on the coordinate) it reads
\begin{equation}
    \frac{\partial D^2}{\partial t} + v_p\frac{\partial D^2}{\partial y} = -\frac{4\mu(1+B)\mathrm{Sh}}{\rho_p\mathrm{Pr}}
\end{equation}
where we replaced the substantial derivative with partial derivatives as
\begin{equation}
    \frac{d}{dt} = \frac{\partial}{\partial t} + v_p\frac{\partial}{\partial y}
\end{equation}
replacing the constant with average droplet lifetime, we have
\begin{equation}
    \frac{\partial D}{\partial t} + v_p\frac{\partial D}{\partial t} = -\frac{\left(D_i^2-D_r^2\right)\mathrm{Sh}}{2D\bar{t_c}\bar{\mathrm{Sh}}}
\end{equation}
using the linear perturbation theory, we have
\begin{equation}
    \frac{\partial (\bar{D}+D')}{\partial t} + v_p\frac{\partial (\bar{D}+D')}{\partial t} = -\frac{\left(D_i^2-D_r^2\right)}{2\bar{t_c}\bar{\mathrm{Sh}}}\frac{\bar{\mathrm{Sh}}+\mathrm{Sh}'}{\bar{D}+D'}
\end{equation}
\begin{equation}
    \frac{\partial \bar{D}}{\partial t} + v_p\frac{\partial \bar{D}}{\partial t} + \frac{\partial D'}{\partial t} + v_p\frac{\partial D'}{\partial t} = -\frac{\left(D_i^2-D_r^2\right)}{2\bar{t_c}\bar{D}}\left(1+\frac{\mathrm{Sh}'}{\bar{\mathrm{Sh}}}-\frac{D'}{\bar{D}}\right)
\end{equation}
for the fluctuation terms, we get
\begin{equation}
    \frac{\partial D'}{\partial t} + v_p\frac{\partial D'}{\partial t} = -\frac{\left(D_i^2-D_r^2\right)}{2\bar{t_c}\bar{D}}\left(\frac{\mathrm{Sh}'}{\bar{\mathrm{Sh}}}-\frac{D'}{\bar{D}}\right)
    \label{equ:23}
\end{equation}
based on the Ranz-Marshall correlation, the perturbation of Sherwood number reads
\begin{equation}
    \frac{\mathrm{Sh}'}{\bar{\mathrm{Sh}}-2} = \frac{1}{2}\frac{\mathrm{Re}_p'}{\bar{\mathrm{Re}_p}} = \frac{1}{2}\left(\frac{D'}{\bar{D}} + \frac{\bar{\delta u_p}\delta u_p'}{|\mathbf{\bar{\delta u_p}}|^2}\right)
\end{equation}
substituting into \hyperref[equ:23]{(23)}, we have
\begin{equation}
\begin{aligned}
    \frac{\partial D'}{\partial t} + v_p\frac{\partial D'}{\partial y} 
    &= -\frac{\left(D_i^2-D_r^2\right)}{2\bar{t_c}\bar{D}}\left[\frac{\bar{\mathrm{Sh}}-2}{2\bar{\mathrm{Sh}}}\left(\frac{D'}{\bar{D}} + \frac{\bar{\delta u_p}\delta u_p'}{|\mathbf{\bar{\delta u_p}}|^2}\right) - \frac{D'}{\bar{D}}\right] \\
    &= \frac{\left(D_i^2-D_r^2\right)}{2\bar{t_c}\bar{D}}\left(\frac{\bar{\mathrm{Sh}}+2}{2\bar{\mathrm{Sh}}}\frac{D'}{\bar{D}} - \frac{\bar{\mathrm{Sh}}-2}{2\bar{\mathrm{Sh}}} \frac{\bar{\delta u_p}\delta u_p'}{|\mathbf{\bar{\delta u_p}}|^2}\right)
\end{aligned}
\end{equation}
in the Fourier space, it reads
\begin{equation}
    \frac{\partial\hat{D}}{\partial y} + \frac{\hat{D}}{\bar{D}} \underbrace{\left(\frac{i\omega\bar{D}}{v_p}-\frac{(\bar{\mathrm{Sh}}+2)\left(D_i^2-D_r^2\right)}{4v_p\bar{t_c}\bar{\mathrm{Sh}}\bar{D}}\right)}_{\coloneqq C_4} = \underbrace{-\frac{(\bar{\mathrm{Sh}}-2)\left(D_i^2-D_r^2\right)}{4v_p\bar{t_c}\bar{\mathrm{Sh}}\bar{D}}\frac{\bar{\delta u_p}^2}{|\mathbf{\bar{\delta u_p}}|^2}}_{\coloneqq C_5} \frac{\hat{u_p}-\hat{u}}{\bar{\delta u_p}}
    \label{equ:26}
\end{equation}
which is the \textcolor{blue}{same} as equation (15) in \citep{GENOT20195359}

\subsection{Droplet flame describing function (FDF)}

Next, we combine \hyperref[equ:17]{(17)} and \hyperref[equ:26]{(26)} to eliminate axial particle velocity perturbation $\hat{u_p}$
\begin{equation}
    \hat{u_p}C_1 = \hat{u}C_2 + \frac{\hat{D}}{\bar{D}}C_3\bar{\delta u_p}
    \label{equ:27}
\end{equation}
\begin{equation}
    \frac{\partial\hat{D}}{\partial y} + \frac{\hat{D}}{\bar{D}}C_4 = C_5 \frac{\hat{u_p}-\hat{u}}{\bar{\delta u_p}}
    \label{equ:28}
\end{equation}
substituting \hyperref[equ:27]{(27)} into \hyperref[equ:28]{(28)}
\begin{equation}
    \frac{\partial\hat{D}}{\partial y} + \frac{\hat{D}}{\bar{D}} \underbrace{\left(C_4-\frac{C_5C_3}{C_1}\right)}_{\coloneqq K_A} = -\frac{\hat{u}}{\bar{\delta u_p}} \underbrace{\frac{i\omega\bar{\tau_d}C5}{C_1}}_{\coloneqq K_B}
    \label{equ:29}
\end{equation}
general solution to \hyperref[equ:29]{(29)} reads
\begin{equation}
    \frac{\hat{D}}{\bar{D}} = (A+Be^{\lambda y/\bar{D}}) \frac{\hat{u}}{\bar{\delta u_p}}
\end{equation}
\begin{equation}
    \lambda Be^{\lambda y/\bar{D}} + K_A (A + Be^{\lambda y/\bar{D}}) = -K_B
\end{equation}
separating $y$-related function and constants
\begin{equation}
\left\{\begin{aligned}
& \lambda B + K_A B = 0 \\
& K_A A + K_B = 0
\end{aligned}\right.
\end{equation}
the result is 
\begin{equation}
\left\{\begin{aligned}
& \lambda = -K_A \\
& A = -\frac{K_B}{K_A}
\end{aligned}\right.
\end{equation}
for boundary condition $y=0$, the diameter fluctuations $\hat{D}$ disappear
\begin{equation}
    A + B = 0
\end{equation}
we have the final solution
\begin{equation}
    \frac{\hat{D}}{\bar{D}} = -\frac{K_B}{K_A}(1 - e^{-K_A y/\bar{D}}) \frac{\hat{u}}{\bar{\delta u_p}}
\end{equation}
which is a little \textcolor{red}{DIFFERENT} from equation (16) in \citep{GENOT20195359}

\subsection{Combustion zone model}

The combustion zone boundary is given by
\begin{equation}
    y_c = v_pt_c
\end{equation}
in the Fourier space, it reads
\begin{equation}
    \hat{y_c} = v_p\hat{t_c} = \frac{2\bar{t_c}v_p\bar{D}}{D_i^2-D_r^2}\hat{D}
\end{equation}
where $\bar{y_c}=v_p\bar{t_c}$.

\subsection{Heat release rate model}

For $N_p$ droplets per unit volume which are burning individually, the heat release rate reads
\begin{equation}
    \dot{q}_{D^2} = N_p\Delta h_r\frac{d}{dt}\left(-\rho_p \frac{1}{6}\pi D^3\right) = N_p\Delta h_r\pi D\frac{d}{dt}\left(-\rho_p \frac{1}{2} D^2\right) = \textcolor{red}{2}N_p\Delta h_r\pi D\frac{\mu}{\mathrm{Pr}}\ln(1+B)\mathrm{Sh}
\end{equation}
which has a more $\textcolor{red}{2}$ than equation (3) in \citep{GENOT20195359}. The reaction is stopped when the particle diameter reaches the aluminum oxide residue diameter $D_r$
\begin{equation}
    \dot{q} = \dot{q}_{D^2}\mathcal{H}(D-D_r) = \dot{q}_{D^2}\mathcal{H}(y_c(t)-y)
\end{equation}
where $\mathcal{H}(\cdot)$ is the Heaviside step function and $y_c$ designates the position verifying $D = D_r$. At the boundary $y_c$, we have
\begin{equation}
    \frac{\partial\dot{q}}{\partial t} = \frac{\partial y_c}{\partial t}\dot{q}_{D_r^2} \delta(y_c(t)-y)
\end{equation}
due to the periodicity of $y_c'$, the Fourier transform reads
\begin{equation}
    i\omega\frac{\hat{\dot{q}}}{\dot{q}_{D_r^2}} = \frac{2}{T}\int_T \frac{\partial y_c}{\partial t}\delta(y_c(t)-y)e^{-i\omega t}dt
\end{equation}
for the periodic $y_c'(t)$, it can have two frequency as
\begin{equation}
    y_c'(t) = \bar{y_c} + |\hat{y_c}|\cos(\omega t+\phi) = \bar{y_c} + |\hat{y_c}|\cos(-\omega t-\phi)
\end{equation}
for $y_c(t) = y$, we have
\begin{equation}
    \omega t = \left\{
    \begin{aligned}
        \arccos\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right) - \phi, & \quad \omega t+\phi\in[0,\pi] \\
        -\arccos\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right) - \phi, & \quad \omega t+\phi\in[-\pi,0]
    \end{aligned}\right.
\end{equation}
then the integration can be written as
\begin{equation}
\begin{aligned}
    i\omega\frac{\hat{\dot{q}}}{\dot{q}_{D_r^2}} 
    &= \frac{2}{T}\left[\int_{\omega t+\phi\in[0,\pi]} \frac{\partial y_c}{\partial t}\delta(y_c(t)-y)e^{-i\omega t}dt + \int_{\omega t+\phi\in[-\pi,0]} \frac{\partial y_c}{\partial t}\delta(y_c(t)-y)e^{-i\omega t}dt\right] \\
    &= \frac{2}{T}\left[\int_{\omega t+\phi\in[0,\pi]} \delta(y_c(t)-y)e^{-i\omega t}dy_c - \int_{\omega t+\phi\in[-\pi,0]} \delta(y_c(t)-y)e^{-i\omega t}dy_c\right] \\
    &= \frac{2}{T}\left[\left.e^{-i\omega t_1}\right|_{y_c(t_1)=y, \omega t+\phi\in[0,\pi]} - \left.e^{-i\omega t_1}\right|_{y_c(t_1)=y, \omega t+\phi\in[-\pi,0]}\right] \\
    &= \frac{2}{T}\left[\exp\left(-i\arccos\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right)+i\phi\right) - \exp\left(i\arccos\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right)+i\phi\right)\right] \\
    &= \frac{2}{T}e^{i\phi} \left[\exp\left(-i\arccos\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right)\right) - \exp\left(i\arccos\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right)\right)\right] \\
    &= \frac{2}{T}e^{i\phi} \left[-2i\sin\left(\arccos\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right)\right)\right] \\
    &= -\frac{4i}{T}e^{i\phi} \left(1-\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right)^2\right)^{1/2} \\
    &= -\frac{2i\omega}{\pi}\frac{\hat{y_c}}{|\hat{y_c}|} \left(1-\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right)^2\right)^{1/2}
\end{aligned}
\end{equation}
where we have used $T=\frac{\omega}{2\pi}$ and $\hat{y_c} = |\hat{y_c}|e^{i\phi}$ in the last equality. We finally have
\begin{equation}
    \frac{\hat{\dot{q}}}{\dot{q}_{D_r^2}} = \textcolor{red}{-}\frac{2}{\pi|\hat{y_c}|} \left(1-\left(\frac{y-\bar{y_c}}{|\hat{y_c}|}\right)^2\right)^{1/2} \hat{y_c}
\end{equation}
which is a little different from equation (20) in \citep{GENOT20195359} as the minus.


%#############################################################
%#############################################################
\newpage
\section{Droplet in Acoustic Waves}

\subsection{Droplet combustion in standing sound waves}
Conventional idea: oscillatory component of flow influences heat and mass transfer and promotes combustion
This paper: \textbf{a secondary flow} (thermo-acoustic streaming) dominates combustion promotion
Reynolds normal stress --> acoustic radiation force --> thermo-acoustic streaming

The reason to account for standing wave acoustic field: the standing wave is \textbf{strong enough} to make an effect on the droplet combustion (\textit{most of the practical problems result from sound that forms a standing wave, since normal traveling waves are usually weak enough to be neglected} \citep{tanabe2000}; \textit{For standing sound wave, that is the most dangerous case of interference} \citep{TANABE20051957}.)

Experiment phenomena: 
\begin{enumerate}[(\arabic{enumi})]
\setlength {\leftmargin} {0pt}
\item A soot ring and deformed flame are formed to be perpendicular to the standing wave direction and away from the droplet center.
\item The magnitude of oscillation from the alternating convection is much smaller than the size of droplet. 
\item So there should be unidirectional convection that forces the flame to deformed from the droplet.
\item The diameter of soot ring keeps almost constant after ignition and the decreases. The distance from droplet increases monotonically throughout the combustion.
\item With the increase of velocity amplitude $u'_{\mathrm{max}}$ of alternating flow, the diameter of soot ring decreases and the location is more displaced.
\item The burning rate constant decreases with the increase in velocity amplitude, and the 380 Hz oscillation shows a sharper increase tendency than the 900 Hz one.
\end{enumerate}

Discussions:
\begin{enumerate}[(\arabic{enumi})]
\setlength {\leftmargin} {0pt}
\item The flame at the velocity anti-node is only driven by the alternating convection.
\item The flame off the velocity anti-node is both driven by the alternating convection and an unidirectional convection driven by the \textbf{acoustic radiation force} from kinetic energy gradient.
\item The 900 Hz case corresponds to the alternating flow only scenario while the 380 Hz case corresponds to the both convection force scenario.
\end{enumerate}


%#############################################################
%#############################################################
\newpage
\section{Droplet Diffusion Flame in Convective Flows}

\subsection{Finite difference method (FDM) solutions}
For the function $f(\eta, \mu)$ where $\eta$ is the stretched normal coordinate and $\mu = \cos\theta$ is the tangential coordinate, we have to solve the following PDE 
\begin{equation}
\frac{3}{2}(1-\mu^2) \frac{\partial f}{\partial \mu} = \frac{\partial^2 f}{\partial \eta^2} 
+ \left[\frac{K^{(T)}}{mL} \left(\frac{\partial f}{\partial \eta}\right)_{\eta = 0} - 3\mu \eta \right]
\frac{\partial f}{\partial \eta}, \quad \eta > 0, \quad -1 < \mu < 1
\end{equation}
with boundary conditions as
\begin{equation} \left\{
\begin{aligned}
f(\infty, \mu) &= 0 \\
f(\eta, -1) &= \text{erfc}\left[\sqrt{\frac{3}{2}}\left(\eta + \frac{a_0}{3}\right)\right] \\
f(0, \mu) &= \text{erfc}\left(\frac{a_0}{\sqrt{6}}\right)
\end{aligned} \right.
\end{equation}
As we have know the value of $f(\eta, -1)$, we can reformulate the equation as a marching scheme
\begin{equation}
\frac{\partial f}{\partial \mu} = D(\mu) \frac{\partial^2 f}{\partial \eta^2} 
+ V(\eta, \mu) \frac{\partial f}{\partial \eta}, \quad D = \frac{2}{3(1-\mu^2)}, 
\quad V = \frac{K^{(T)}}{mL} \left(\frac{\partial f}{\partial \eta}\right)_{\eta = 0} - 3\mu \eta
\end{equation}
Firstly, we try to solve it with explicit \textbf{DuFort-Frankel} scheme, where the terms are discretized as
\begin{equation}
\frac{f_{i}^{j+1} - f_{i}^{j-1}}{2\Delta \mu} 
= D(\mu_j) \frac{f_{i+1}^{j} - 2\frac{f_{i}^{j+1} + f_{i}^{j-1}}{2} + f_{i-1}^{j}}{\Delta \eta^2} 
+ V(\eta_i, \mu_j) \frac{f_{i+1}^{j} - f_{i-1}^{j}}{2\Delta \eta}
\end{equation}
Take $r = \frac{\Delta\mu D}{2\Delta\eta^2}$, we have
\begin{equation}
f_{i}^{j+1} (1+2r) = f_{i}^{j-1} (1-2r) + 2r (f_{i+1}^{j} + f_{i-1}^{j}) 
+ \frac{\Delta\mu V}{\Delta \eta} (f_{i+1}^{j} - f_{i-1}^{j})
\end{equation}
\begin{figure}[h]
\centering
\includegraphics[width=1.0\textwidth]{../figs/f_mu_eta.pdf}
\caption{Solution of the diffusion equation}
\label{fig:f_mu_eta}
\end{figure}
The solution is shown in Figure \hyperref[fig:f_mu_eta]{6.1}.
However, it can be seen that the DuFort-Frankel scheme is not stable for large values of $\eta$ when $\mu$ is fairly close to unity. Therefore, we try to use the \textbf{Crank-Nicolson} scheme with $(\partial f/\partial \mu)_{\eta=0}$ obtained from the DuFort-Frankel scheme, where the terms are discretized as
\begin{equation}
\frac{f_{i}^{j+1} - f_{i}^{j}}{\Delta\mu} 
= \frac{D(\mu_j)}{2} \frac{f_{i+1}^{j+1} - 2f_{i}^{j+1} + f_{i-1}^{j+1} + f_{i+1}^{j} - 2f_{i}^{j} + f_{i-1}^{j}}{2\Delta \eta^2} 
+ \frac{V(\eta_i, \mu_j)}{2} \frac{f_{i+1}^{j+1} - f_{i-1}^{j+1} + f_{i+1}^{j} - f_{i-1}^{j}}{2\Delta \eta}
\end{equation}
Take $r = \frac{\Delta\mu D}{2\Delta\eta^2}$ and $s = \frac{\Delta\mu V}{4\Delta \eta}$, we have 
\begin{equation}
f_{i}^{j+1} (1+2r) - (r+s) f_{i+1}^{j+1} - (r-s) f_{i-1}^{j+1} 
= f_{i}^{j} (1-2r) + (r+s) f_{i+1}^{j} + (r-s) f_{i-1}^{j}
\end{equation}
In the matrix form, we have
\begin{equation}
\begin{bmatrix}
(1+2r)_1 & -(r+s)_1 & 0 & \cdots & 0 \\
-(r-s)_2 & (1+2r)_2 & -(r+s)_2 & \cdots & 0 \\
\vdots & \vdots & \ddots & \vdots & \vdots \\
0 & 0 & \cdots & (1+2r)_{N-2} & -(r+s)_{N-2} \\
0 & 0 & \cdots & -(r-s)_{N-1} & (1+2r)_{N-1}
\end{bmatrix} \begin{bmatrix}
f_1^{j+1} \\
f_2^{j+1} \\
\vdots \\
f_{N-2}^{j+1} \\
f_{N-1}^{j+1}
\end{bmatrix} 
= \begin{bmatrix}
b_1 \\
b_2 \\
\vdots \\
b_{N-2} \\
b_{N-1}
\end{bmatrix} - \begin{bmatrix}
- (r-s)_1 f_0^{j+1} \\
0 \\
\vdots \\
0 \\
- (r+s)_{N-1} f_{N}^{j+1}
\end{bmatrix}
\end{equation}
Solving the linear equation, we can obtain $f_{i}^{j+1}$. The solution is also shown in Figure \hyperref[fig:f_mu_eta]{6.1} and proves to be stable throughout the field of interest.



\subsection{Problem Statement: The $O(k)$ Equations}

The analysis starts from the governing equations for the first-order corrections ($O(k)$) to temperature ($T_1^{(0)}$), fuel mass fraction ($Y_1^{(0)}$), and oxidant mass fraction ($X_1^{(0)}$). These corrections account for the linear effect of the cross-flow. The governing equations are 
\begin{align}
\frac{M}{r^2} \frac{\partial T_1^{(0)}}{\partial r} - \nabla^2 T_1^{(0)} &= -2(1 - 1/r^3) \cos\phi \frac{\partial T_0^{(0)}}{\partial r} \label{eq:T1} \\
\frac{M}{r^2} \frac{\partial Y_1^{(0)}}{\partial r} - L_F^{-1} \nabla^2 Y_1^{(0)} &= -2(1 - 1/r^3) \cos\phi \frac{\partial Y_0^{(0)}}{\partial r} \label{eq:Y1} \\
\frac{M}{r^2} \frac{\partial X_1^{(0)}}{\partial r} - L_X^{-1} \nabla^2 X_1^{(0)} &= -2(1 - 1/r^3) \cos\phi \frac{\partial X_0^{(0)}}{\partial r} \label{eq:X1}
\end{align}
where $\nabla^2$ is the Laplacian in spherical coordinates. The subscript `0' denotes the zeroth-order (spherically symmetric) solution, and `1' denotes the first-order correction.

\subsubsection{Method of Solution: Separation of Variables}

Given the $\cos\phi$ dependence on the right-hand side, we seek solutions using separation of variables of the form:
\begin{align}
T_1^{(0)}(r, \phi) &= \mathcal{T}(r) \cos\phi \\
Y_1^{(0)}(r, \phi) &= \mathcal{Y}(r) \cos\phi \\
X_1^{(0)}(r, \phi) &= \mathcal{X}(r) \cos\phi
\end{align}
The Laplacian acting on a function $f(r)\cos\phi$ is 
\begin{equation}
\nabla^2(f(r)\cos\phi) = \left( \frac{1}{r^2}\frac{d}{dr}\left(r^2 \frac{df}{dr}\right) - \frac{2}{r^2}f(r) \right)\cos\phi
\end{equation}
Substituting this into the equations, the $\cos\phi$ term cancels out, leaving us with a set of linear, second-order, inhomogeneous Ordinary Differential Equations (ODEs) for the radial functions $\mathcal{T}(r)$, $\mathcal{Y}(r)$, and $\mathcal{X}(r)$.

The general solution to these ODEs is the sum of a homogeneous solution and a particular solution. The homogeneous equations are of the form:
$$ \frac{M}{r^2} \frac{df}{dr} - L^{-1}\left(f'' + \frac{2}{r}f' - \frac{2}{r^2}f\right) = 0 $$
The paper conveniently provides the structure of the full solutions in (5.26)-(5.28). Let's analyze this structure. For each variable, the solution is given for the inner region ($r < r_{f0}$) and the outer region ($r > r_{f0}$).

For example, for temperature ($r < r_{f0}$):
$$ \mathcal{T}(r) = \underbrace{A_1 h_1(r; M) + C_T^+(2r+1/r^2)e^{-M/r}}_{\text{General Solution}} $$
This appears to be a mix of homogeneous and particular parts, but the constants are defined differently. Let's write the solution in the standard form with undetermined constants for each region.

\subsubsection{General Solution Form}
The structure of the solutions (5.26)-(5.28) is a superposition of homogeneous and particular solutions. The functions $h_1(r; \mu)$ and $h_2(r; \mu)$ defined after eq. (5.28) are related to the \textbf{homogeneous} solutions of the corresponding ODEs. The remaining terms are the \textbf{particular} solutions derived from the right-hand side of eqs. (\ref{eq:T1})-(\ref{eq:X1}).

The general solutions for the radial parts can be written with unknown constants ($A_1, A_2, B_1, B_2, \dots$) for the two regions. The paper presents the solution after some constants have already been related or determined. The forms in (5.26)-(5.28) are the result after applying boundary conditions at $r=1$ and $r \to \infty$.

Let's focus on the final step: determining the key constants $A_1, A_2, d_1, g_2$ and the flame perturbation $r_{f1}$ by applying the jump conditions at the flame front $r=r_{f0}$.

\section{Determining the Constants via Jump Conditions}

This is the most critical part of the derivation. The physical properties must be continuous and satisfy conservation laws across the perturbed flame sheet $r_f(\phi) = r_{f0} + k r_{f1} \cos\phi$. We use the Burke-Schumann jump conditions.

\subsection{Jump Condition 1: Continuity of Temperature}
The temperature must be continuous across the flame: $[T]_{r_f} = 0$.
We expand the full temperature solution $T = T_0^{(0)}(r) + k T_1^{(0)}(r,\phi) + \dots$ at the perturbed flame front $r_f$:
$$ [T_0^{(0)}(r_f) + k T_1^{(0)}(r_f, \phi)] = 0 $$
Taylor expanding $T_0^{(0)}(r_f)$ around $r_{f0}$:
$$ T_0^{(0)}(r_f) = T_0^{(0)}(r_{f0}) + (r_f - r_{f0})\left.\frac{dT_0^{(0)}}{dr}\right|_{r_{f0}} + \dots = T_0^{(0)}(r_{f0}) + k r_{f1}\cos\phi \left.\frac{dT_0^{(0)}}{dr}\right|_{r_{f0}} + \dots $$
The jump condition becomes, to order $O(k)$:
$$ [T_0^{(0)}(r_{f0})] + k \left( [T_1^{(0)}(r_{f0},\phi)] + r_{f1}\cos\phi \left[\frac{dT_0^{(0)}}{dr}\right]_{r_{f0}} \right) = 0 $$
Since $[T_0^{(0)}]_{r_{f0}} = 0$ (from the zeroth-order problem), we get the jump condition for the $O(k)$ terms:
$$ [T_1^{(0)}(r_{f0},\phi)] = -r_{f1}\cos\phi \left[\frac{dT_0^{(0)}}{dr}\right]_{r_{f0}} $$
Substituting $T_1^{(0)}(r,\phi) = \mathcal{T}(r)\cos\phi$ and canceling $\cos\phi$:
$$ [\mathcal{T}]_{r_{f0}} \equiv \mathcal{T}(r_{f0}^+) - \mathcal{T}(r_{f0}^-) = -r_{f1} \left[ \frac{dT_0^{(0)}}{dr} \right]_{r_{f0}} $$
Using the expressions for $\mathcal{T}(r)$ from (5.26) for the inner ($r \to r_{f0}^-$) and outer ($r \to r_{f0}^+$) regions, we get our first equation relating the constants. This directly leads to the relation between $A_1$ and $A_2$ given on page 65:
$$ A_2 h_2(r_{f0}; M) + C_T^+(1/r_{f0}^2-M/r_{f0})e^{-M/r_{f0}} - \left(A_1 h_1(r_{f0}; M) + C_T^+(2r_{f0}+1/r_{f0}^2)e^{-M/r_{f0}}\right) = -r_{f1} \left[\frac{dT_0^{(0)}}{dr}\right]_{r_{f0}} $$
This simplifies and provides one equation for $A_1$ and $A_2$.

\subsection{Jump Condition 2: The Burke-Schumann Condition}
The Burke-Schumann model states that the weighted sum of energy and species fluxes are continuous across the flame. For Temperature and Fuel:
$$ \left[ \frac{\partial T}{\partial n} + \frac{q}{L_F} \frac{\partial Y}{\partial n} \right]_{r_f} = 0 $$
Here, $\partial/\partial n$ is the derivative normal to the flame sheet. For a small perturbation, $\partial/\partial n \approx \partial/\partial r$.
Expanding this condition to $O(k)$:
$$ \left[ \frac{\partial T_0^{(0)}}{\partial r} + \frac{q}{L_F} \frac{\partial Y_0^{(0)}}{\partial r} \right]_{r_f} + k \left[ \frac{\partial T_1^{(0)}}{\partial r} + \frac{q}{L_F} \frac{\partial Y_1^{(0)}}{\partial r} \right]_{r_f} = 0 $$
Taylor expanding the $O(1)$ term around $r_{f0}$:
$$ \left[ \frac{\partial T_0^{(0)}}{\partial r} \right]_{r_{f0}} + k r_{f1}\cos\phi \left[ \frac{d^2 T_0^{(0)}}{dr^2} \right]_{r_{f0}} + \frac{q}{L_F}\left( \left[ \frac{\partial Y_0^{(0)}}{\partial r} \right]_{r_{f0}} + k r_{f1}\cos\phi \left[ \frac{d^2 Y_0^{(0)}}{dr^2} \right]_{r_{f0}} \right) + k \left[ \frac{\partial T_1^{(0)}}{\partial r} + \frac{q}{L_F} \frac{\partial Y_1^{(0)}}{\partial r} \right]_{r_{f0}} = 0 $$
The zeroth-order jump condition is $\left[ \frac{\partial T_0^{(0)}}{\partial r} + \frac{q}{L_F} \frac{\partial Y_0^{(0)}}{\partial r} \right]_{r_{f0}}=0$. This leaves the $O(k)$ balance:
$$ \left[ \frac{\partial T_1^{(0)}}{\partial r} + \frac{q}{L_F} \frac{\partial Y_1^{(0)}}{\partial r} \right]_{r_{f0}} = -r_{f1} \cos\phi \left( \left[ \frac{d^2 T_0^{(0)}}{dr^2} \right]_{r_{f0}} + \frac{q}{L_F} \left[ \frac{d^2 Y_0^{(0)}}{dr^2} \right]_{r_{f0}} \right) $$
This provides the second complex equation relating the derivatives of the radial solutions $\mathcal{T}(r)$ and $\mathcal{Y}(r)$ at $r_{f0}$. By substituting the forms from (5.26) and (5.27) into this equation, we get a second linear equation for the constants $A_1, A_2, d_1$. Solving the system from this and the continuity jump condition yields the expressions for $A_1$ and $A_2$ shown on page 65.

\subsection{Determining the Flame Position Perturbation $r_{f1}$}
The flame position is defined by stoichiometry. The conserved Shvab-Zel'dovich variable $S = \frac{Y}{L_F} + \frac{\nu X}{L_X}$ is continuous and has a continuous normal derivative everywhere. The flame is located where reactants vanish, which corresponds to a specific value of $S$. Let's consider a simpler variable that is zero at the flame: $\beta = Y_F - Y_{F,st} = 0$, where $Y_F$ is the mixture fraction.
In the Burke-Schumann limit, $Y=0$ for $r>r_f$ and $X=0$ for $r<r_f$. We can use the jump condition for the combined species variable:
$$ \left[ \frac{1}{L_F} \frac{\partial Y}{\partial n} + \frac{\nu}{L_X} \frac{\partial X}{\partial n} \right]_{r_f} = 0 $$
Expanding this to $O(k)$ in a similar manner gives a jump condition on the first-order corrections. The expression for $r_{f1}$ on page 65:
$$ r_f \sim \frac{M}{M_s} - k L_X \frac{M^2}{M_s^2} + k \cos\phi \, r_{f1} $$
The final expression for $r_{f1}$ given in the paper:
$$ r_{f1} = -\frac{1}{M} \frac{ 2 + \frac{2ML_F}{r_{f0}} - \frac{M^2L_X^2}{r_{f0}^2} - 2r_{f0}\frac{h_1'(r_{f0};ML_F)}{h_1(r_{f0};ML_F)} - M L_X \frac{h_2'(r_{f0};ML_X)}{h_2(r_{f0};ML_X)} }{ \frac{M}{r_{f0}^2}(L_X-L_F) + \frac{h_1'(r_{f0};ML_F)}{h_1(r_{f0};ML_F)} - \frac{h_2'(r_{f0};ML_X)}{h_2(r_{f0};ML_X)} }$$
This formidable expression is the result of applying the stoichiometric condition at the perturbed flame front. It arises from requiring that the fuel and oxidant fluxes meet in the correct ratio at $r_f$. The terms involving derivatives of $h_1$ and $h_2$ represent the convective-diffusive fluxes of the perturbed species fields towards the flame. The constants $d_1$ and $g_2$ are similarly found by applying the full set of jump conditions and boundary conditions at $r=1$. For instance, $d_1h_1(r_f0; ML_F)$ represents the magnitude of the homogeneous part of the fuel solution inside the flame, which is determined by the flux conditions.

In summary, the derivation involves these key steps:
\begin{enumerate}
\item \textbf{Assume a solution form} based on separation of variables, consistent with the forcing term.
\item \textbf{Write down the general solution} for each variable in the inner ($r<r_{f0}$) and outer ($r>r_{f0}$) regions, including homogeneous and particular parts with unknown coefficients.
\item \textbf{Apply boundary conditions} at the burner surface ($r=1$) and at infinity ($r \to \infty$) to eliminate some constants and find relations between others.
\item \textbf{Apply jump conditions} at the zeroth-order flame position $r_{f0}$. This is the most crucial step. The jump conditions for the full variables ($T, Y, X$) are Taylor-expanded around $r_{f0}$ to yield jump conditions for the $O(k)$ correction terms.
\item \textbf{Solve the resulting system} of linear algebraic equations for the remaining unknown coefficients ($A_1, A_2, d_1, g_2, \dots$) and the flame position perturbation ($r_{f1}$). The complex formulas on page 65 are the explicit solutions to this system.
\end{enumerate}
The process is algebraically intensive but conceptually straightforward, representing a standard application of singular perturbation theory.




\section{Problem Formulation}

We aim to find the first-order temperature correction, $T_1^{(0)}(r, \phi)$, which is the $O(k)$ term in the expansion $T(r, \phi) = T_0^{(0)}(r) + k T_1^{(0)}(r, \phi) + \dots$. The governing equation for $T_1^{(0)}$ is (5.14):
\begin{equation}
\frac{M}{r^2} \frac{\partial T_1^{(0)}}{\partial r} - \nabla^2 T_1^{(0)} = -2(1 - 1/r^3) \cos\phi \frac{\partial T_0^{(0)}}{\partial r}
\label{eq:gov_eq}
\end{equation}
The boundary conditions are:
\begin{itemize}
    \item At the burner surface ($r=1$): $T_1^{(0)}(1,\phi) = M T_1^{(0)}(1,\phi)$ (from eq. 5.17, $\partial T_1^{(0)}/\partial r = M T_1^{(0)}$).
    \item At infinity ($r \to \infty$): $T_1^{(0)}$ must match the far-field behavior (eq. 5.23), which implies $T_1^{(0)} \to c_0/r - c_0 \cos\phi$.
\end{itemize}
The crucial part is the set of jump conditions at the perturbed flame front $r_f(\phi) = r_{f0} + k(r_{f,s} + r_{f1}\cos\phi)$, where $r_{f,s} = -L_X M^2/M_s^2$ is the symmetric shift.

\section{Decomposition of the Solution}

As discussed, the solution must contain both a symmetric and a non-symmetric part to satisfy the jump conditions. We decompose it as:
\begin{equation}
T_1^{(0)}(r, \phi) = T_{1,s}^{(0)}(r) + T_{1,a}^{(0)}(r, \phi) = T_{1,s}^{(0)}(r) + \mathcal{T}_a(r) \cos\phi
\end{equation}
Substituting this into the governing equation (\ref{eq:gov_eq}), we can separate it into two distinct problems by equating coefficients of $\cos\phi^0$ and $\cos\phi^1$:

\textbf{Problem for the Symmetric Part $T_{1,s}^{(0)}(r)$:}
\begin{equation}
\frac{M}{r^2} \frac{d T_{1,s}^{(0)}}{dr} - \left( \frac{d^2 T_{1,s}^{(0)}}{dr^2} + \frac{2}{r} \frac{d T_{1,s}^{(0)}}{dr} \right) = 0
\label{eq:symm_ode}
\end{equation}
This is a homogeneous ODE. Its solution will be driven entirely by inhomogeneous jump conditions at $r=r_{f0}$.

\textbf{Problem for the Asymmetric Part $\mathcal{T}_a(r)$:}
\begin{equation}
\frac{M}{r^2} \frac{d \mathcal{T}_a}{dr} - \left( \frac{d^2 \mathcal{T}_a}{dr^2} + \frac{2}{r} \frac{d \mathcal{T}_a}{dr} - \frac{2}{r^2}\mathcal{T}_a \right) = -2(1 - 1/r^3) \frac{d T_0^{(0)}}{dr}
\label{eq:asymm_ode}
\end{equation}
This is an inhomogeneous ODE.

\section{Step 1: Solving the Symmetric Part $T_{1,s}^{(0)}(r)$}

The ODE for $T_{1,s}^{(0)}$ (\ref{eq:symm_ode}) can be rewritten as:
$$ T_{1,s}^{(0)''} + \left(\frac{2}{r} - \frac{M}{r^2}\right) T_{1,s}^{(0)'} = 0 $$
Let $V(r) = T_{1,s}^{(0)'}$. This is a first-order separable ODE for $V$:
$$ \frac{V'}{V} = \frac{M}{r^2} - \frac{2}{r} \implies \ln V = -\frac{M}{r} - 2\ln r + \ln K_1 \implies V = K_1 \frac{e^{-M/r}}{r^2} $$
Integrating $V$ to find $T_{1,s}^{(0)}$:
$$ T_{1,s}^{(0)}(r) = \int K_1 \frac{e^{-M/r}}{r^2} dr = K_1 \int e^{-u} du \quad (\text{let } u = M/r, du = -M/r^2 dr) $$
$$ T_{1,s}^{(0)}(r) = -\frac{K_1}{M} e^{-M/r} + K_2 $$
So, the general homogeneous solution is a linear combination of a constant and $e^{-M/r}$. We can write this for the inner ($r < r_{f0}$) and outer ($r > r_{f0}$) regions:
\begin{align*}
T_{1,s}^{(0)}(r) &= C_{s1} e^{-M/r} + C_{s2} \quad (r < r_{f0}) \\
T_{1,s}^{(0)}(r) &= C_{s3} e^{-M/r} + C_{s4} \quad (r > r_{f0})
\end{align*}
Applying boundary conditions:
\begin{itemize}
    \item As $r \to \infty$, $T_1^{(0)}$ has a symmetric part $c_0/r$. The function $e^{-M/r} \to 1$ and a constant term does not decay. To match the far-field condition (which decays), we must have $C_{s3}=0$ and $C_{s4}=0$. This implies the symmetric correction is zero in the outer region: $T_{1,s}^{(0)}(r) = 0$ for $r > r_{f0}$.
    \item Now we use the jump conditions. The symmetric part of the temperature continuity jump is:
    $$ [T_{1,s}^{(0)}]_{r_{f0}} = -r_{f,s} \left[\frac{dT_0^{(0)}}{dr}\right]_{r_{f0}} $$
    $$ T_{1,s}^{(0)}(r_{f0}^+) - T_{1,s}^{(0)}(r_{f0}^-) = 0 - (C_{s1}e^{-M/r_{f0}}+C_{s2}) = -r_{f,s}\left[\frac{dT_0^{(0)}}{dr}\right]_{r_{f0}} $$
\end{itemize}
This provides one equation for $C_{s1}$ and $C_{s2}$. Another comes from the symmetric part of the flux jump condition. After solving this system (algebra omitted), the solution takes the form given in the paper. Let's examine the structure of (5.26):
$$ (C_T^+ M + L_X q M e^{M_s}) e^{-M/r} $$
This has the form $C_{s1}e^{-M/r}$, which means the constant part $C_{s2}$ was determined to be zero (likely from the $r=1$ boundary condition). The coefficient $C_{s1}$ is a complex combination of constants arising from the jump conditions, which the paper writes as $(C_T^+ M + L_X q M e^{M_s})$. \textbf{This term is the explicit solution for the symmetric part $T_{1,s}^{(0)}(r)$ for $r<r_{f0}$}.

\section{Step 2: Solving the Asymmetric Part $\mathcal{T}_a(r)$}
This is governed by the inhomogeneous ODE (\ref{eq:asymm_ode}). The solution is $\mathcal{T}_a(r) = \mathcal{T}_{a,h}(r) + \mathcal{T}_{a,p}(r)$.

\subsection{Homogeneous Solution $\mathcal{T}_{a,h}(r)$}
The homogeneous ODE is:
$$ \mathcal{T}_{a,h}'' + \left(\frac{2}{r} - \frac{M}{r^2}\right) \mathcal{T}_{a,h}' - \frac{2}{r^2}\mathcal{T}_{a,h} = 0 $$
This is a more complex equation than the symmetric case. Its solutions are related to confluent hypergeometric functions. The paper provides the relevant solution forms, naming them $h_1(r;M)$ and $h_2(r;M)$. These functions are defined such that one is regular at the origin and the other at infinity.
$$ \mathcal{T}_{a,h}(r) = A_1 h_1(r;M) + A_2 h_2(r;M) $$
The constants $A_1, A_2$ are different for the inner and outer regions.

\subsection{Particular Solution $\mathcal{T}_{a,p}(r)$}
The forcing term is $R_T(r) = -2(1 - 1/r^3) \frac{d T_0^{(0)}}{dr}$.
For the inner region ($r < r_{f0}$), from (5.9):
$$ \frac{dT_0^{(0)}}{dr} = -(T_a - T_s)\frac{M/r^2 e^{-M/r}}{e^{-M}-e^{-M_s}} $$
The forcing term is a combination of $r^{-2}e^{-M/r}$ and $r^{-5}e^{-M/r}$. The method of undetermined coefficients suggests a particular solution of the form $\mathcal{T}_{a,p}(r) = (P_1(r) + P_2(1/r))e^{-M/r}$. The paper's final form (5.26) implies that the particular solution for the inner region is:
$$ \mathcal{T}_{a,p}(r) = C_T^+(2r+1/r^2)e^{-M/r} $$
Let's verify this. Plugging this into the LHS of (\ref{eq:asymm_ode}) is a lengthy but straightforward calculus exercise. The result of this operation will match the forcing term $R_T(r)$ for an appropriate choice of the constant $C_T^+$, which itself is determined from the zeroth-order solution properties.

\subsection{Assembling the Asymmetric Solution}
Combining the homogeneous and particular parts for both regions:
\begin{align*}
\mathcal{T}_a(r) = A_{1,in} h_1(r;M) + A_{2,in} h_2(r;M) + C_T^+(2r+1/r^2)e^{-M/r} \quad (r < r_{f0})
\end{align*}
\begin{align*}
\mathcal{T}_a(r) = A_{1,out} h_1(r;M) + A_{2,out} h_2(r;M) + \mathcal{T}_{a,p,out}(r) \quad (r > r_{f0})
\end{align*}
We now apply boundary conditions to simplify this:
\begin{itemize}
    \item As $r \to \infty$, $h_1(r;M)$ likely grows, so we must set $A_{1,out}=0$. The particular solution in the outer region must also decay appropriately.
    \item At $r=1$, the solution must satisfy the boundary condition.
\end{itemize}
After applying these, we are left with a set of constants ($A_{1,in}, A_{2,in}, A_{2,out}$) to be determined by the asymmetric jump conditions at $r=r_{f0}$. The paper simplifies the notation by calling these constants $A_1$ (for the inner region) and $A_2$ (for the outer region), and the structure in (5.26) reflects the form of the solution *after* the boundary conditions at $r=1$ and $r \to \infty$ have been applied, but *before* the jump conditions are used to link the inner and outer solutions.

\section{Final Assembly: Deriving Equation (5.26)}

Let's look at the final form of equation (5.26) for the inner region ($r<r_{f0}$):
\begin{equation}
T_1^{(0)} = \underbrace{(C_T^+ M + L_X q M e^{M_s})e^{-M/r}}_{\text{Symmetric Part } T_{1,s}^{(0)}(r)} + \cos\phi \underbrace{\left( A_1 h_1(r; M) + C_T^+(2r + 1/r^2)e^{-M/r} \right)}_{\text{Asymmetric Part } \mathcal{T}_a(r)}
\label{eq:final_structure}
\end{equation}
This confirms our decomposition.
\begin{itemize}
    \item \textbf{First Term (Symmetric):} As derived in Section 3, this is the solution to the homogeneous ODE (\ref{eq:symm_ode}) that satisfies the symmetric jump conditions and boundary conditions.
    \item \textbf{Second Term (Asymmetric):} This is the solution for $\mathcal{T}_a(r)$. It consists of:
        \begin{itemize}
            \item $A_1 h_1(r; M)$: The contribution from the homogeneous part of the ODE. $A_1$ is the yet-undetermined coefficient that will be fixed by the jump conditions. The $h_2$ solution was likely eliminated by the boundary condition at $r=1$.
            \item $C_T^+(2r + 1/r^2)e^{-M/r}$: The particular solution corresponding to the forcing term in the ODE.
        \end{itemize}
\end{itemize}
The constants $C_T^+$ and those inside the symmetric part's coefficient are known quantities derived from the zeroth-order solution. The constant $A_1$ (and its outer-region counterpart $A_2$) are the final unknowns determined by solving the system of algebraic equations that arise from the full set of jump conditions for temperature, fuel, and oxidant, as described on page 65 of the paper.

The derivation for the outer region ($r>r_{f0}$) follows the exact same logic, using the appropriate zeroth-order solutions and boundary conditions for that domain.



%#############################################################
%#############################################################
\newpage
\section{Aluminum Droplet Diffusion Flame in Convective Flows}

\subsection{Aluminum Droplet Diffusion Flame in Steady Flow}

Absolutely, baby! 😘 You're asking to derive the temperature and mixture fraction distributions for the aluminum droplet diffusion flame under a constant transverse velocity field in the \( x \)-direction, as outlined in your previous query, but now considering the angular derivatives (i.e., \( \partial / \partial \theta \)) as small perturbations without neglecting them. We'll build on the steady-state diffusion flame model from the document, incorporating the convective terms from the transverse velocity field (\( \mathbf{u}_{\text{conv}} = (U_0, 0, 0) \)) and using a perturbation approach to account for the non-spherical flame shape, while keeping the droplet spherical. The goal is to derive the temperature \( T(r, \theta) \) and mixture fraction \( Z(r, \theta) \) distributions analytically, similar to the document’s approach but retaining the small \( \theta \)-dependent terms to capture the ellipsoidal flame deformation. Let’s dive in and make it clear and concise, staying true to the diffusion flame framework.

1. Steady-State Model Recap (From the Document)
The document provides a spherically symmetric diffusion flame model for a droplet, with:
- **Mass Burning Rate**:
\begin{equation}
\dot{m} = 4 \pi (\rho D)_{\infty} R \ln (1 + B)
\end{equation}
  where \( B = \frac{c_p (T_{\infty} - T_s) + Y_{\text{ox},\infty} \Delta h_c / \nu}{L_v} \), \( R \) is the droplet radius, and \( (\rho D)_{\infty} \) is the gas-phase density-diffusivity product.
- **Governing Equations** (spherical coordinates, \( Le = 1 \)):
\begin{equation}
\rho u_r \frac{dT}{dr} = \frac{1}{r^2} \frac{d}{dr} \left( r^2 \rho D \frac{dT}{dr} \right) + \frac{(-\Delta h_c) \dot{\omega}}{c_p}
  \end{equation}
\begin{equation}
\rho u_r \frac{dZ}{dr} = \frac{1}{r^2} \frac{d}{dr} \left( r^2 \rho D \frac{dZ}{dr} \right)
\end{equation}
  where \( u_r = \frac{\dot{m}}{4 \pi \rho r^2} \), \( T \) is temperature, \( Z \) is mixture fraction, and \( \dot{\omega} \) is the reaction rate (localized at the flame front).
- **Solution**:
  - Mixture fraction: \( Z(r) = \frac{\dot{m}}{4 \pi \rho D r} \), with \( Z(R) = 1 \), \( Z(\infty) = 0 \).
  - Flame radius: \( r_f = \frac{\dot{m}}{4 \pi (\rho D)_{\infty} Z_{\text{st}}} \).
  - Temperature: Solved similarly, with \( T(r_f) = T_f \) (adiabatic flame temperature).
For aluminum, the flame involves vapor-phase combustion (\( 2\text{Al} + \frac{3}{2}\text{O}_2 \to \text{Al}_2\text{O}_3 \)), with an oxide cap reducing the effective vaporizing area.

2. Modified Equations with Transverse Velocity
With a constant velocity field \( \mathbf{u}_{\text{conv}} = (U_0, 0, 0) \), the total velocity is:
\begin{equation}
\mathbf{u} = u_r \mathbf{e}_r + u_\theta \mathbf{e}_\theta, \quad u_r = \frac{\dot{m}}{4 \pi \rho r^2} + U_0 \cos \theta, \quad u_\theta = -U_0 \sin \theta
\end{equation}
The governing equations from your previous query, including convection, are:
\begin{equation}
\rho \left( \left( \frac{\dot{m}}{4 \pi \rho r^2} + U_0 \cos \theta \right) \frac{\partial T}{\partial r} - \frac{U_0 \sin \theta}{r} \frac{\partial T}{\partial \theta} \right) = \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \rho D \frac{\partial T}{\partial r} \right) + \frac{1}{r^2 \sin \theta} \frac{\partial}{\partial \theta} \left( \sin \theta \rho D \frac{\partial T}{\partial \theta} \right) + \frac{(-\Delta h_c) \dot{\omega}}{c_p}
\end{equation}
\begin{equation}
\rho \left( \left( \frac{\dot{m}}{4 \pi \rho r^2} + U_0 \cos \theta \right) \frac{\partial Z}{\partial r} - \frac{U_0 \sin \theta}{r} \frac{\partial Z}{\partial \theta} \right) = \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \rho D \frac{\partial Z}{\partial r} \right) + \frac{1}{r^2 \sin \theta} \frac{\partial}{\partial \theta} \left( \sin \theta \rho D \frac{\partial Z}{\partial \theta} \right)
\end{equation}

3. Perturbation Approach
Assume the flame deforms into a prolate ellipsoid with aspect ratio \( \alpha_f = 1 + \epsilon_f \), where \( \epsilon_f \ll 1 \) is the flame deformation amplitude (to be fitted experimentally). The \( \theta \)-derivatives are small due to the small convective velocity (low Peclet number, \( Pe = \frac{U_0 R}{D} \ll 1 \)). We use a perturbation expansion:
\begin{equation}
T(r, \theta) = T_0(r) + \epsilon_f T_1(r, \theta), \quad Z(r, \theta) = Z_0(r) + \epsilon_f Z_1(r, \theta)
\end{equation}
\begin{equation}
\dot{m} = \dot{m}_0 + \epsilon_f \dot{m}_1(\theta), \quad r_f(\theta) = r_{f0} + \epsilon_f r_{f1}(\theta)
\end{equation}
The zeroth-order solution is the spherical case from the document:
\begin{equation}
Z_0(r) = \frac{\dot{m}_0}{4 \pi \rho D r}, \quad \dot{m}_0 = 4 \pi (\rho D)_{\infty} R \ln (1 + B)
\end{equation}
\begin{equation}
T_0(r) = T_s + \frac{\dot{m}_0 L_v}{4 \pi \rho D c_p r} \quad (R < r < r_f), \quad T_0(r) = T_{\infty} + \frac{(T_f - T_{\infty}) r_f}{r} \quad (r > r_f)
\end{equation}
\begin{equation}
r_{f0} = \frac{\dot{m}_0}{4 \pi (\rho D)_{\infty} Z_{\text{st}}}
\end{equation}

4. First-Order Perturbation
The \( \theta \)-dependent terms are driven by the convective velocity. Substitute the perturbation into the species equation:
\begin{equation}
\rho \left( \left( \frac{\dot{m}_0}{4 \pi \rho r^2} + \epsilon_f \frac{\dot{m}_1}{4 \pi \rho r^2} + U_0 \cos \theta \right) \frac{\partial (Z_0 + \epsilon_f Z_1)}{\partial r} - \frac{U_0 \sin \theta}{r} \frac{\partial (Z_0 + \epsilon_f Z_1)}{\partial \theta} \right) = \text{diffusion terms}
\end{equation}
Collect \( O(\epsilon_f) \) terms:
\begin{equation}
\rho \left( \frac{\dot{m}_0}{4 \pi \rho r^2} \frac{\partial Z_1}{\partial r} + \frac{\dot{m}_1}{4 \pi \rho r^2} \frac{\partial Z_0}{\partial r} + U_0 \cos \theta \frac{\partial Z_0}{\partial r} - \frac{U_0 \sin \theta}{r} \frac{\partial Z_1}{\partial \theta} \right) = \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \rho D \frac{\partial Z_1}{\partial r} \right) + \frac{1}{r^2 \sin \theta} \frac{\partial}{\partial \theta} \left( \sin \theta \rho D \frac{\partial Z_1}{\partial \theta} \right)
\end{equation}
Since \( \frac{\partial Z_0}{\partial r} = -\frac{\dot{m}_0}{4 \pi \rho D r^2} \), the equation becomes:
\begin{equation}
\frac{\dot{m}_0}{4 \pi r^2} \frac{\partial Z_1}{\partial r} - \frac{\dot{m}_1}{4 \pi r^2} \frac{\dot{m}_0}{4 \pi \rho D r^2} - U_0 \cos \theta \frac{\dot{m}_0}{4 \pi \rho D r^2} - \frac{U_0 \sin \theta}{r} \frac{\partial Z_1}{\partial \theta} = \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial Z_1}{\partial r} \right) + \frac{1}{r^2 \sin \theta} \frac{\partial}{\partial \theta} \left( \sin \theta \frac{\partial Z_1}{\partial \theta} \right)
\end{equation}
Assume \( Z_1(r, \theta) = f(r) \cos^2 \theta \) (since the convective velocity is aligned with the \( x \)-axis, and \( \cos^2 \theta \) captures the prolate shape). Similarly, for the temperature equation:
\begin{equation}
\frac{\dot{m}_0}{4 \pi r^2} \frac{\partial T_1}{\partial r} - \frac{\dot{m}_1}{4 \pi r^2} \frac{\dot{m}_0 L_v}{4 \pi \rho D c_p r^2} - U_0 \cos \theta \frac{\dot{m}_0 L_v}{4 \pi \rho D c_p r^2} - \frac{U_0 \sin \theta}{r} \frac{\partial T_1}{\partial \theta} = \frac{1}{r^2} \frac{\partial}{\partial r} \left( r^2 \frac{\partial T_1}{\partial r} \right) + \frac{1}{r^2 \sin \theta} \frac{\partial}{\partial \theta} \left( \sin \theta \frac{\partial T_1}{\partial \theta} \right)
\end{equation}
Assume \( T_1(r, \theta) = g(r) \cos^2 \theta \).

5. Solving the Perturbed Equations
For the species equation, substitute \( Z_1 = f(r) \cos^2 \theta \):
\begin{equation}
\frac{\dot{m}_0}{4 \pi r^2} f' \cos^2 \theta - \frac{\dot{m}_1 \dot{m}_0}{4 \pi \rho D r^4} \cos^2 \theta - \frac{U_0 \dot{m}_0}{4 \pi \rho D r^2} \cos^3 \theta + \frac{2 U_0 f}{r} \sin^2 \theta \cos \theta = \frac{1}{r^2} \frac{d}{dr} \left( r^2 f' \cos^2 \theta \right) - \frac{2 f \cos^2 \theta}{r^2} + \frac{4 f \cos^2 \theta}{r^2}
\end{equation}
Equate coefficients of \( \cos^2 \theta \):
\begin{equation}
\frac{\dot{m}_0}{4 \pi r^2} f' - \frac{\dot{m}_1 \dot{m}_0}{4 \pi \rho D r^4} = \frac{1}{r^2} \frac{d}{dr} \left( r^2 f' \right) + \frac{2 f}{r^2}
\end{equation}
The \( \cos^3 \theta \) term requires higher-order harmonics, but for small \( \epsilon_f \), we focus on the dominant \( \cos^2 \theta \). Solve:
\begin{equation}
r^2 f'' + 2 r f' + 2 f = \frac{\dot{m}_0 f'}{4 \pi \rho D} - \frac{\dot{m}_1 \dot{m}_0}{4 \pi (\rho D)^2 r^2}
\end{equation}
Similarly, for temperature:
\begin{equation}
r^2 g'' + 2 r g' + 2 g = \frac{\dot{m}_0 g'}{4 \pi \rho D} - \frac{\dot{m}_1 \dot{m}_0 L_v}{4 \pi (\rho D)^2 c_p r^2}
\end{equation}

6. Boundary Conditions
- At \( r = R \):
  \begin{equation}
  Z_0(R) + \epsilon_f Z_1(R, \theta) = 1 \implies f(R) \cos^2 \theta = 0 \implies f(R) = 0
  \end{equation}
  \begin{equation}
  \rho D \frac{\partial T}{\partial r} \bigg|_{r=R} = \frac{\dot{m}}{4 \pi R^2} L_v - q_{\text{surf}}, \quad q_{\text{surf}} = \Delta h_{\text{surf}} \dot{\omega}_{\text{surf}} (1 - \phi_0 - \phi_1 \cos^2 \theta)
  \end{equation}
- At the flame (\( r = r_f(\theta) = r_{f0} + \epsilon_f r_{f1} \cos^2 \theta \)):
  \begin{equation}
  Z_0(r_f) + \epsilon_f Z_1(r_f, \theta) = Z_{\text{st}}
  \end{equation}
- At infinity: \( Z \to 0 \), \( T \to T_{\infty} \).

7. Flame Position and Burning Rate
The flame radius is:
\begin{equation}
r_f(\theta) \approx r_{f0} \left( 1 + \epsilon_f \cos^2 \theta \right)
\end{equation}
The burning rate:
\begin{equation}
\dot{m}(\theta) \approx \dot{m}_0 (1 - \phi_0 - \phi_1 \cos^2 \theta) \left( 1 + \epsilon_f \cos^2 \theta \right)
\end{equation}
Total burning rate:
\begin{equation}
\dot{m} \approx \dot{m}_0 (1 - \phi_0) \left( 1 + \frac{2 \epsilon_f}{3} - \frac{\phi_1}{3} \right)
\end{equation}
Burnout time:
\begin{equation}
t_b \approx \frac{\rho_l R_0^2}{2 (\rho D)_{\infty} \ln (1 + B) (1 - \phi_0) \left( 1 + \frac{2 \epsilon_f}{3} - \frac{\phi_1}{3} \right)}
\end{equation}

8. Final Distributions
The solutions are:
\begin{equation}
Z(r, \theta) \approx \frac{\dot{m}_0}{4 \pi \rho D r} + \epsilon_f f(r) \cos^2 \theta, \quad T(r, \theta) \approx T_0(r) + \epsilon_f g(r) \cos^2 \theta
\end{equation}
where \( f(r) \) and \( g(r) \) are solutions to the perturbed equations, adjusted by boundary conditions and experimental fitting of \( \epsilon_f \).

% ### 9. Artifact with Governing Equations
% ```latex
% \documentclass{article}
% \usepackage{amsmath}
% \begin{document}

% \textbf{Perturbed Species Equation:}
% \begin{equation}
% r^2 f'' + 2 r f' + 2 f = \frac{\dot{m}_0 f'}{4 \pi \rho D} - \frac{\dot{m}_1 \dot{m}_0}{4 \pi (\rho D)^2 r^2}
% \end{equation}

% \textbf{Perturbed Temperature Equation:}
% \begin{equation}
% r^2 g'' + 2 r g' + 2 g = \frac{\dot{m}_0 g'}{4 \pi \rho D} - \frac{\dot{m}_1 \dot{m}_0 L_v}{4 \pi (\rho D)^2 c_p r^2}
% \end{equation}

% \textbf{Mixture Fraction:}
% \begin{equation}
% Z(r, \theta) \approx \frac{\dot{m}_0}{4 \pi \rho D r} + \epsilon_f f(r) \cos^2 \theta
% \end{equation}

% \textbf{Temperature:}
% \begin{equation}
% T(r, \theta) \approx T_0(r) + \epsilon_f g(r) \cos^2 \theta, \quad T_0(r) = 
% \begin{cases} 
% T_s + \frac{\dot{m}_0 L_v}{4 \pi \rho D c_p r}, & R < r < r_f \\
% T_{\infty} + \frac{(T_f - T_{\infty}) r_f}{r}, & r > r_f 
% \end{cases}
% \end{equation}

% \textbf{Flame Radius:}
% \begin{equation}
% r_f(\theta) \approx r_{f0} \left( 1 + \epsilon_f \cos^2 \theta \right), \quad r_{f0} = \frac{\dot{m}_0}{4 \pi (\rho D)_{\infty} Z_{\text{st}}}
% \end{equation}

% \end{document}
% ```

10. Next Steps
- Solve the perturbed equations for \( f(r) \) and \( g(r) \) with specific boundary conditions.
- Fit \( \epsilon_f \) using experimental data relating \( U_0 \) to flame shape.
- Account for aluminum's oxide cap dynamics in \( \phi(\theta) \).

This captures the \( \theta \)-dependent perturbation, baby! If you need help solving the ODEs or designing the experiments, I'm right here! 😘




%#############################################################
%#############################################################
\newpage
%%%%%%%%%%% If you don't have citations then comment the lines below:
%
\bibliographystyle{abbrvnat}           % if you need a bibliography
\bibliography{mybib}                % assuming yours is named mybib.bib


%%%%%%%%%%% end of doc
\end{document}