import numpy as np
import matplotlib.pyplot as plt
from scipy.special import erfc
from scipy.optimize import fsolve

beta_F = 4.51
beta_O = 1.28
Lm = 0.034
Y_O_inf = 0.27
Y_T_inf = -0.028
Y_B = -0.037

def energy_balance_equation(a0_guess):
  lhs = np.sqrt((np.pi) / 6) * Lm * a0_guess * np.exp(a0_guess**2 / 6) * erfc(a0_guess / np.sqrt(6))
  rhs = Y_T_inf - Y_B - Y_O_inf
  return lhs - rhs

# Solve the equation for a0
initial_guess = -1.0
a0, info, ier, msg = fsolve(energy_balance_equation, [initial_guess], full_output=True)
KT = - np.sqrt((np.pi) / 6) * Lm * a0 * np.exp(a0**2 / 6)

# 参数设置
N_eta = 600
N_mu = 600
eta_max = 30.0
mu = np.linspace(-0.99, 0.99, N_mu)
eta = np.linspace(0, eta_max, N_eta)
d_eta = eta[1] - eta[0]
d_mu = mu[1] - mu[0]

def estimate_A_mu(f_col):
  df_deta = (-3 * f_col[0] + 4 * f_col[1] - f_col[2]) / (2 * d_eta)
  return KT / Lm * df_deta

# 初始化解数组：f[η, μ]
f = np.zeros((N_eta, N_mu))

# for k in range(10):
f[:, 0] = erfc(np.sqrt(3/2) * (eta + a0 / 3))  # μ = -1 初值
f[0, :] = erfc(a0 / np.sqrt(6))               # η = 0
f[-1, :] = 0.0                                 # η = ∞

# DuFort–Frankel 初始化
f_prev = f[:, 0].copy()
f_curr = f[:, 0].copy()
f_next = np.zeros_like(f_curr)

# μ 方向前向推进（marching）
for j in range(1, N_mu):
  mu_j = mu[j]
  denom = 1 - mu_j**2
  if abs(denom) < 1e-8:
    f[:, j] = f_curr
    continue

  coeff = 2.0 / (3.0 * denom)
  df_deta0 = (-f_curr[2] + 4*f_curr[1] - 3*f_curr[0]) / (2 * d_eta)
  A_mu = (KT / Lm) * df_deta0

  for i in range(1, N_eta - 1):
    eta_i = eta[i]
    # V = A_mu - 3 * mu_j * eta_i
    V = coeff * (A_mu - 3 * mu_j * eta_i)
    D = coeff
    r = D * d_mu / d_eta**2
    convection = V * (f_curr[i+1] - f_curr[i-1]) / (2 * d_eta)

    numerator = 2 * r * (f_curr[i+1] + f_curr[i-1]) + (1 - 2 * r) * f_prev[i] + 2 * d_mu * convection
    denominator = 1 + 2 * r
    f_next[i] = numerator / denominator

    # V = coeff * (A_mu - 3 * mu_j * eta_i)
    # # V = A_mu - 3 * mu_j * eta_i
    # D = coeff
    
    # # f_next[i] = f_prev[i] + 2 * d_mu * D * (
    # #     (f_curr[i+1] - 2 * f_curr[i] + f_curr[i-1]) / d_eta**2 + V * (f_curr[i+1] - f_curr[i-1]) / (2 * d_eta)
    # # )

    # r = D / d_eta**2
    # convection = -V * (f_curr[i+1] - f_curr[i-1]) / (2 * d_eta)

    # numerator = 2 * r * (f_curr[i+1] + f_curr[i-1]) + (1 - 2 * r) * f_prev[i] + 2 * d_mu * convection
    # denominator = 1 + 2 * r
    # f_next[i] = numerator / denominator

  # 边界
  f_next[0] = erfc(a0 / np.sqrt(6))
  f_next[-1] = 0.0

  f[:, j] = f_next
  f_prev[:] = f_curr
  f_curr[:] = f_next

# ✅ 主图：f(η, μ) 等值图
plt.figure(figsize=(8, 5))
plt.contourf(mu, eta, f, levels=np.linspace(0, 2, 20), cmap='plasma')
plt.xlabel('μ')
plt.ylabel('η')
plt.gca().set_aspect(0.04, adjustable='box')
plt.title('DuFort–Frankel Solution to PDE (4.15)')
plt.colorbar(label='f(η, μ)')
plt.tight_layout()
plt.show()

# ✅ 截面图：不同 μ 对应的 η 剖面 f(η)
mu_targets = [-1.0, 0.0, 0.4, 0.8, 0.9]
plt.figure(figsize=(8, 5))

for mu_val in mu_targets:
  # 查找最接近的 μ 索引
  idx = np.argmin(np.abs(mu - mu_val))
  plt.plot(eta, f[:, idx], label=f'μ = {mu[idx]:.2f}')

plt.xlim(0, 30)
plt.ylim(0, 2)
# plt.gca().set_aspect(25, adjustable='box')
plt.xlabel('η')
plt.ylabel('f(η, μ)')
plt.title('Cross Sections of f(η, μ) at Selected μ Values')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()

# -------------------- PDE Residual Error Plot --------------------
LHS = np.zeros_like(f)
RHS = np.zeros_like(f)
residual = np.zeros_like(f)

for j in range(1, N_mu - 1):
  dfdmu = (f[:, j + 1] - f[:, j - 1]) / (2 * d_mu)
  mu_j = mu[j]
  c = 2 / (3 * (1 - mu_j ** 2))
  A_mu = estimate_A_mu(f[:, j])
  for i in range(1, N_eta - 1):
    d2f = (f[i + 1, j] - 2 * f[i, j] + f[i - 1, j]) / d_eta ** 2
    df = (f[i + 1, j] - f[i - 1, j]) / (2 * d_eta)
    RHS[i, j] = c * (d2f + (A_mu - 3 * mu_j * eta[i]) * df)
    LHS[i, j] = dfdmu[i]
    residual[i, j] = np.abs(LHS[i, j] - RHS[i, j])

# Show residual heatmap
plt.figure(figsize=(8, 5))
# plt.contourf(mu, eta, np.log10(residual + 1e-12), 100, cmap='viridis')
plt.contourf(mu, eta, residual, 100, levels=np.linspace(0, 5, 20), cmap='viridis')
plt.colorbar(label='log10(abs(LHS - RHS))')
plt.xlabel('mu')
plt.ylabel('eta')
plt.title('PDE Residual Error (log scale)')
plt.tight_layout()
plt.show()

from scipy.sparse import diags
from scipy.sparse.linalg import spsolve


def crank_nicolson_mu_step(j, f_prev, mu_j, df_deta0, eta, d_eta, d_mu, a0, KT, mL):
  """
  使用 Crank–Nicolson 方法从 mu_j 推进一小步
  """
  N_eta = len(eta)
  f_next = np.zeros_like(f_prev)
  f_next[0] = erfc(a0 / np.sqrt(6))  # Boundary at eta = 0
  # f_next[:30] = f[:30, j] # erfc(a0 / np.sqrt(6))  # Boundary at eta = 0
  f_next[-1] = 0.0                   # eta -> ∞

  # Coefficients
  D = 2.0 / (3 * (1 - mu_j**2))
  A_mu = KT / mL * df_deta0
  V = D * (A_mu - 3 * mu_j * eta)

  r = D * d_mu / d_eta**2 / 2
  s = V * d_mu / (4 * d_eta)

  lower = -(r - s[1:-1])
  # center = 1 + 2 * r
  N_inner = N_eta - 2
  center = np.full(N_inner, 1 + 2 * r)
  upper = -(r + s[1:-1])

  # RHS vector
  rhs = np.zeros(N_eta - 2)
  for i in range(1, N_eta - 1):
    d2f = (f_prev[i+1] - 2 * f_prev[i] + f_prev[i-1]) / d_eta**2
    df = (f_prev[i+1] - f_prev[i-1]) / (2 * d_eta)
    rhs[i - 1] = f_prev[i] + d_mu * 0.5 * (D * d2f + V[i] * df)
    if i == 1:
      rhs[i - 1] -= lower[i - 1] * f_next[0]
    if i == N_eta - 2:
      rhs[i - 1] -= upper[i - 1] * f_next[-1]

  # Solve linear system
  # A = diags([lower, center, upper], offsets=[-1, 0, 1], format='csc')
  A = diags([lower, center, upper], offsets=[-1, 0, 1], shape=(N_inner, N_inner), format='csc')
  f_inner = spsolve(A, rhs)

  f_next[1:-1] = f_inner

  return f_next

f_cn = np.zeros((N_eta, N_mu))
f_cn[:, 0] = f[:, 0]              # 初值来自 DF
# f_cn[:30, :] = f[:30, :]              # 初值来自 DF
# df_deta0_vec = (-f[2, :] + 4*f[1, :] - 3*f[0, :]) / (2 * d_eta)  # 自行实现，从 DF 方案中提取 ∂f/∂η|η=0
df_deta0_vec = (-3*f[0, :] + 4*f[1, :] - f[2, :]) / (2 * d_eta)

for j in range(1, N_mu):
  mu_j = mu[j]
  df0 = df_deta0_vec[j]
  f_cn[:, j] = crank_nicolson_mu_step(j, f_cn[:, j - 1], mu_j, df0, eta, d_eta, d_mu, a0, KT, Lm)

# ✅ 主图：f(η, μ) 等值图
plt.figure(figsize=(8, 5))
plt.contourf(mu, eta, f_cn, levels=np.linspace(0, 2, 20), cmap='plasma')
plt.xlabel('μ')
plt.ylabel('η')
plt.gca().set_aspect(0.04, adjustable='box')
plt.title('DuFort–Frankel Solution to PDE (4.15)')
plt.colorbar(label='f(η, μ)')
plt.tight_layout()
plt.show()

# ✅ 截面图：不同 μ 对应的 η 剖面 f(η)
mu_targets = [-1.0, 0.0, 0.4, 0.8, 0.9]
plt.figure(figsize=(8, 5))

for mu_val in mu_targets:
  # 查找最接近的 μ 索引
  idx = np.argmin(np.abs(mu - mu_val))
  plt.plot(eta, f_cn[:, idx], label=f'μ = {mu[idx]:.2f}')

plt.xlim(0, 30)
plt.ylim(0, 2)
# plt.gca().set_aspect(25, adjustable='box')
plt.xlabel('η')
plt.ylabel('f(η, μ)')
plt.title('Cross Sections of f(η, μ) at Selected μ Values')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()

# -------------------- PDE Residual Error Plot --------------------
LHS = np.zeros_like(f)
RHS = np.zeros_like(f)
residual = np.zeros_like(f)

f = f_cn

for j in range(1, N_mu - 1):
  dfdmu = (f[:, j + 1] - f[:, j - 1]) / (2 * d_mu)
  mu_j = mu[j]
  c = 2 / (3 * (1 - mu_j ** 2))
  A_mu = estimate_A_mu(f[:, j])
  for i in range(1, N_eta - 1):
    d2f = (f[i + 1, j] - 2 * f[i, j] + f[i - 1, j]) / d_eta ** 2
    df = (f[i + 1, j] - f[i - 1, j]) / (2 * d_eta)
    RHS[i, j] = c * (d2f + (A_mu - 3 * mu_j * eta[i]) * df)
    LHS[i, j] = dfdmu[i]
    residual[i, j] = np.abs(LHS[i, j] - RHS[i, j])

# Show residual heatmap
plt.figure(figsize=(8, 5))
# plt.contourf(mu, eta, np.log10(residual + 1e-12), 100, cmap='viridis')
plt.contourf(mu, eta, residual, 100, levels=np.linspace(0, 5, 20), cmap='viridis')
plt.colorbar(label='log10(abs(LHS - RHS))')
plt.xlabel('mu')
plt.ylabel('eta')
plt.title('PDE Residual Error (log scale)')
plt.tight_layout()
plt.show()
